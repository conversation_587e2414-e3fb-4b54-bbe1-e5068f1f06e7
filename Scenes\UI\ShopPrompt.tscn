[gd_scene load_steps=6 format=3 uid="uid://c8k7x2n8nap1q"]

[ext_resource type="Script" uid="uid://d0ha4yeyaymmj" path="res://Scripts/ShopPrompt.gd" id="1_prompt"]
[ext_resource type="Theme" uid="uid://dvwgvp8u8xo0i" path="res://Scenes/UI/Themes/UI_Theme.tres" id="2_theme"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.7)
corner_radius_top_left = 15
corner_radius_top_right = 15
corner_radius_bottom_right = 15
corner_radius_bottom_left = 15

[sub_resource type="Animation" id="Animation_1"]
resource_name = "float"
length = 2.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1, 2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(0, -10), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 1, 2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0.8), Color(1, 1, 1, 1), Color(1, 1, 1, 0.8)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
&"float": SubResource("Animation_1")
}

[node name="ShopPrompt" type="Control"]
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_prompt")

[node name="Control" type="Control" parent="."]
modulate = Color(1, 1, 1, 0.8)
custom_minimum_size = Vector2(40, 40)
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0
theme = ExtResource("2_theme")

[node name="PromptLabel" type="Label" parent="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 24
theme_override_styles/normal = SubResource("StyleBoxFlat_1")
text = "E"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control"]
libraries = {
&"": SubResource("AnimationLibrary_1")
}
