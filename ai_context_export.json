{"autoloads": {"GameManager": "*res://Scripts/GameManager.gd", "MusicManager": "*res://Scenes/UI/MusicManager.tscn"}, "generated_timestamp": "2025-08-01T12:31:31", "project_name": "GMTK2025-LoopTake2", "project_settings": {"application": {"description": "", "main_scene": "uid://c0pbif8cyy7x7", "name": "GMTK2025-LoopTake2"}, "display": {"height": 648, "width": 1152}, "input": {"map": {"spatial_editor/freelook_backwards": ["InputEventKey: keycode=83 (S), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_down": ["InputEventKey: keycode=81 (Q), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_forward": ["InputEventKey: keycode=87 (W), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_left": ["InputEventKey: keycode=65 (A), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_right": ["InputEventKey: keycode=68 (D), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_slow_modifier": ["InputEventKey: keycode=4194328 (Alt), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_speed_modifier": ["InputEventKey: keycode=4194325 (Shift), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_up": ["InputEventKey: keycode=69 (E), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_orbit_modifier_1": [], "spatial_editor/viewport_orbit_modifier_2": [], "spatial_editor/viewport_pan_modifier_1": ["InputEventKey: keycode=4194325 (Shift), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_pan_modifier_2": [], "spatial_editor/viewport_zoom_modifier_1": ["InputEventKey: keycode=4194326 (Ctrl), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_zoom_modifier_2": [], "ui_accept": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=32 (Space), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_cancel": ["InputEventKey: keycode=4194305 (Escape), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_copy": ["InputEventKey: keycode=67 (C), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194311 (Insert), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_cut": ["InputEventKey: keycode=88 (X), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194312 (Delete), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_down": ["InputEventKey: keycode=4194322 (Down), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_end": ["InputEventKey: keycode=4194318 (End), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_refresh": ["InputEventKey: keycode=4194336 (F5), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_show_hidden": ["InputEventKey: keycode=72 (H), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_up_one_level": ["InputEventKey: keycode=4194308 (Backspace), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_next": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_prev": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_delete": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_duplicate": ["InputEventKey: keycode=68 (D), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_home": ["InputEventKey: keycode=4194317 (Home), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_left": ["InputEventKey: keycode=4194319 (Left), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_menu": ["InputEventKey: keycode=4194370 (Menu), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_page_down": ["InputEventKey: keycode=4194324 (PageDown), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_page_up": ["InputEventKey: keycode=4194323 (PageUp), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_paste": ["InputEventKey: keycode=86 (V), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194311 (Insert), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_redo": ["InputEventKey: keycode=90 (Z), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=89 (Y), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_right": ["InputEventKey: keycode=4194321 (Right), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_select": ["InputEventKey: keycode=32 (Space), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_swap_input_direction": ["InputEventKey: keycode=96 (QuoteLeft), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_add_selection_for_next_occurrence": ["InputEventKey: keycode=68 (D), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_backspace": ["InputEventKey: keycode=4194308 (Backspace), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194308 (Backspace), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_backspace_all_to_left": [], "ui_text_backspace_word": ["InputEventKey: keycode=4194308 (Backspace), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_add_above": ["InputEventKey: keycode=4194320 (Up), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_add_below": ["InputEventKey: keycode=4194322 (Down), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_document_end": ["InputEventKey: keycode=4194318 (End), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_document_start": ["InputEventKey: keycode=4194317 (Home), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_down": ["InputEventKey: keycode=4194322 (Down), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_left": ["InputEventKey: keycode=4194319 (Left), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_line_end": ["InputEventKey: keycode=4194318 (End), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_line_start": ["InputEventKey: keycode=4194317 (Home), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_page_down": ["InputEventKey: keycode=4194324 (PageDown), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_page_up": ["InputEventKey: keycode=4194323 (PageUp), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_right": ["InputEventKey: keycode=4194321 (Right), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_up": ["InputEventKey: keycode=4194320 (Up), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_word_left": ["InputEventKey: keycode=4194319 (Left), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_word_right": ["InputEventKey: keycode=4194321 (Right), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_clear_carets_and_selection": ["InputEventKey: keycode=4194305 (Escape), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_accept": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194309 (Enter), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_query": ["InputEventKey: keycode=32 (Space), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_replace": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_dedent": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_delete": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_delete_all_to_right": [], "ui_text_delete_word": ["InputEventKey: keycode=4194312 (Delete), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_indent": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline_above": ["InputEventKey: keycode=4194309 (Enter), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline_blank": ["InputEventKey: keycode=4194309 (Enter), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_scroll_down": ["InputEventKey: keycode=4194322 (Down), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_scroll_up": ["InputEventKey: keycode=4194320 (Up), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_select_all": ["InputEventKey: keycode=65 (A), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_select_word_under_caret": ["InputEventKey: keycode=71 (G), mods=Alt, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_skip_selection_for_next_occurrence": ["InputEventKey: keycode=68 (D), mods=Ctrl+Alt, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_submit": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_toggle_insert_mode": ["InputEventKey: keycode=4194311 (Insert), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_undo": ["InputEventKey: keycode=90 (Z), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_unicode_start": ["InputEventKey: keycode=85 (U), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_up": ["InputEventKey: keycode=4194320 (Up), mods=none, physical=false, location=unspecified, pressed=false, echo=false"]}}, "physics": {}, "rendering": {}}, "scenes": [{"path": "res:///addons/trail_2d/test.tscn", "root_node": {"children": [{"children": [{"children": [], "groups": [], "name": "Trail2D", "properties": {"length": 10, "script": "res://addons/trail_2d/trail_2d.gd", "width_curve": "res://addons/trail_2d/test.tscn::Curve_ni00x"}, "script": "res://addons/trail_2d/trail_2d.gd", "type": "Line2D"}], "groups": [], "name": "Marker2D", "properties": {}, "script": "", "type": "Marker2D"}, {"children": [{"children": [], "groups": [], "name": "LengthDisplay", "properties": {"label_settings": "res://addons/trail_2d/test.tscn::LabelSettings_g5i13", "layout_mode": 0, "position": [0.0, 19.0], "size": [40.0, 23.0], "text": "Trail length:"}, "script": "", "type": "Label"}], "groups": [], "name": "Slide<PERSON>", "properties": {"min_value": 1.0, "position": [184.0, 19.0], "size": [335.0, 16.0], "theme": "res://addons/trail_2d/test.tscn::Theme_vniel", "value": 10.0}, "script": "", "type": "HSlider"}], "groups": [], "name": "Test", "properties": {"script": "res://addons/trail_2d/test.tscn::GDScript_62opy"}, "script": "res://addons/trail_2d/test.tscn::GDScript_62opy", "type": "Node2D"}}, {"path": "res:///Scenes/Collectable.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"texture": "res://Assets/kenney_simple-space/PNG/Default/star_medium.png"}, "script": "", "type": "Sprite2D"}, {"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/Collectable.tscn::CircleShape2D_1"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollectionParticles", "properties": {"amount": 20, "color": [1.0, 1.0, 0.0, 1.0], "direction": [0.0, -1.0], "emitting": false, "explosiveness": 1.0, "gravity": [0.0, 98.0], "initial_velocity_max": 100.0, "initial_velocity_min": 50.0, "scale_amount_max": 1.5, "scale_amount_min": 0.5}, "script": "", "type": "CPUParticles2D"}], "groups": [], "name": "Collectable", "properties": {"collectable_type": 0, "collection_name": "Star", "minimum_value": 1, "orbit_radius": 150.0, "orbit_speed": 1.0, "point_value": 10, "script": "res://Scripts/Collectable.gd", "value_degradation_amount": 1, "value_degradation_interval": 1.0}, "script": "res://Scripts/Collectable.gd", "type": "Area2D"}}, {"path": "res:///Scenes/Collectables/Collectable_Crystal.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"modulate": [1.0, 0.5, 1.0, 1.0], "texture": "res://Assets/kenney_simple-space/PNG/Default/meteor_detailedSmall.png"}, "script": "", "type": "Sprite2D"}, {"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/Collectables/Collectable_Crystal.tscn::CircleShape2D_1"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollectionParticles", "properties": {"amount": 25, "color": [1.0, 0.0, 1.0, 1.0], "direction": [0.0, -1.0], "emitting": false, "explosiveness": 1.0, "gravity": [0.0, 60.0], "initial_velocity_max": 140.0, "initial_velocity_min": 70.0, "lifetime": 1.2, "scale_amount_max": 1.5, "scale_amount_min": 0.5}, "script": "", "type": "CPUParticles2D"}], "groups": [], "name": "CrystalCollectable", "properties": {"collectable_type": 2, "collection_name": "Crystal", "minimum_value": 1, "orbit_radius": 200.0, "orbit_speed": 0.6, "point_value": 50, "script": "res://Scripts/Collectable.gd", "value_degradation_amount": 1, "value_degradation_interval": 1.0}, "script": "res://Scripts/Collectable.gd", "type": "Area2D"}}, {"path": "res:///Scenes/Collectables/Collectable_EnergyCore.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"modulate": [0.5, 1.0, 0.5, 1.0], "texture": "res://Assets/kenney_simple-space/PNG/Default/effect_yellow.png"}, "script": "", "type": "Sprite2D"}, {"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/Collectables/Collectable_EnergyCore.tscn::CircleShape2D_1"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollectionParticles", "properties": {"amount": 30, "color": [0.0, 1.0, 0.0, 1.0], "direction": [0.0, -1.0], "emitting": false, "explosiveness": 1.0, "gravity": [0.0, 40.0], "initial_velocity_max": 160.0, "initial_velocity_min": 80.0, "lifetime": 1.5, "scale_amount_max": 2.0, "scale_amount_min": 0.600000023841858}, "script": "", "type": "CPUParticles2D"}], "groups": [], "name": "EnergyCoreCollectable", "properties": {"collectable_type": 3, "collection_name": "Energy Core", "minimum_value": 1, "orbit_radius": 250.0, "orbit_speed": 0.4, "point_value": 100, "script": "res://Scripts/Collectable.gd", "value_degradation_amount": 1, "value_degradation_interval": 1.0}, "script": "res://Scripts/Collectable.gd", "type": "Area2D"}}, {"path": "res:///Scenes/Collectables/Collectable_Satellite.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"modulate": [0.800000011920929, 1.0, 1.0, 1.0], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_022.png"}, "script": "", "type": "Sprite2D"}, {"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/Collectables/Collectable_Satellite.tscn::CircleShape2D_1"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollectionParticles", "properties": {"amount": 20, "color": [0.0, 1.0, 1.0, 1.0], "direction": [0.0, -1.0], "emitting": false, "explosiveness": 1.0, "gravity": [0.0, 80.0], "initial_velocity_max": 120.0, "initial_velocity_min": 60.0, "scale_amount_max": 1.20000004768372, "scale_amount_min": 0.400000005960464}, "script": "", "type": "CPUParticles2D"}], "groups": [], "name": "SatelliteCollectable", "properties": {"collectable_type": 1, "collection_name": "Satellite", "minimum_value": 1, "orbit_radius": 180.0, "orbit_speed": 0.8, "point_value": 25, "script": "res://Scripts/Collectable.gd", "value_degradation_amount": 1, "value_degradation_interval": 1.0}, "script": "res://Scripts/Collectable.gd", "type": "Area2D"}}, {"path": "res:///Scenes/Collectables/Collectable_Star.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"modulate": [1.0, 1.0, 0.800000011920929, 1.0], "texture": "res://Assets/kenney_simple-space/PNG/Default/star_medium.png"}, "script": "", "type": "Sprite2D"}, {"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/Collectables/Collectable_Star.tscn::CircleShape2D_1"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollectionParticles", "properties": {"amount": 15, "color": [1.0, 1.0, 0.0, 1.0], "direction": [0.0, -1.0], "emitting": false, "explosiveness": 1.0, "gravity": [0.0, 50.0], "initial_velocity_max": 80.0, "initial_velocity_min": 40.0, "lifetime": 0.8, "scale_amount_min": 0.300000011920929}, "script": "", "type": "CPUParticles2D"}], "groups": [], "name": "StarCollectable", "properties": {"collectable_type": 0, "collection_name": "Star", "minimum_value": 1, "orbit_radius": 120.0, "orbit_speed": 1.5, "point_value": 10, "script": "res://Scripts/Collectable.gd", "value_degradation_amount": 1, "value_degradation_interval": 1.0}, "script": "res://Scripts/Collectable.gd", "type": "Area2D"}}, {"path": "res:///Scenes/Game.tscn", "root_node": {"children": [{"children": [{"children": [{"children": [{"children": [], "groups": [], "name": "ScoreLabel", "properties": {"label_settings": "res://Scenes/UI/GameHUD.tscn::LabelSettings_4y6w4", "layout_mode": 2, "text": "Score: 0"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "BoostsLabel", "properties": {"label_settings": "res://Scenes/UI/GameHUD.tscn::LabelSettings_4y6w4", "layout_mode": 2, "text": "Boosts: 1"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "BoostPowerLabel", "properties": {"label_settings": "res://Scenes/UI/GameHUD.tscn::LabelSettings_4y6w4", "layout_mode": 2, "text": "Launch Power: 0%"}, "script": "", "type": "Label"}], "groups": [], "name": "VBoxContainer", "properties": {"layout_mode": 0, "position": [15.0, 15.0], "size": [250.0, 89.0]}, "script": "", "type": "VBoxContainer"}, {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "expand_mode": 1, "layout_mode": 1, "stretch_mode": 5, "texture": "res://Assets/Circle.png"}, "script": "", "type": "TextureRect"}, {"children": [{"children": [], "groups": [], "name": "HomeIcon", "properties": {"anchors_preset": 8, "expand_mode": 1, "layout_mode": 1, "position": [-8.0, -8.0], "size": [16.0, 16.0], "stretch_mode": 5, "texture": "res://Assets/kenney_simple-space/PNG/Default/station_A.png"}, "script": "", "type": "TextureRect"}, {"children": [], "groups": [], "name": "PlanetIcons", "properties": {"anchors_preset": 15, "layout_mode": 1}, "script": "", "type": "Control"}], "groups": [], "name": "CompassCenter", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-55.0, -55.0], "size": [110.0, 110.0]}, "script": "", "type": "Control"}], "groups": [], "name": "<PERSON>mp<PERSON>", "properties": {"anchors_preset": 1, "layout_mode": 1, "map_radius": 50.0, "map_scale": 0.01, "position": [-120.0, 10.0], "script": "res://Scripts/Compass.gd", "size": [110.0, 110.0]}, "script": "res://Scripts/Compass.gd", "type": "Control"}, {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "layout_mode": 1, "theme_override_styles/panel": "res://Scenes/UI/ObjectivesPanel.tscn::StyleBoxFlat_1"}, "script": "", "type": "Panel"}, {"children": [{"children": [], "groups": [], "name": "TitleLabel", "properties": {"horizontal_alignment": 1, "label_settings": "res://Scenes/UI/ObjectivesPanel.tscn::LabelSettings_title", "layout_mode": 2, "text": "OBJECTIVES"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "HSeparator", "properties": {"layout_mode": 2}, "script": "", "type": "HSeparator"}], "groups": [], "name": "VBoxContainer", "properties": {"anchors_preset": 15, "layout_mode": 1}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "ObjectivesPanel", "properties": {"layout_mode": 0, "position": [15.0, 120.0], "script": "res://Scripts/ObjectivesPanel.gd", "size": [250.0, 300.0], "visible": false}, "script": "res://Scripts/ObjectivesPanel.gd", "type": "Control"}, {"children": [], "groups": [], "name": "NotificationContainer", "properties": {"anchors_preset": 3, "layout_mode": 1, "position": [-250.0, -100.0], "size": [250.0, 100.0]}, "script": "", "type": "Control"}], "groups": [], "name": "GameHUD", "properties": {"anchors_preset": 15, "mouse_filter": 2, "script": "res://Scripts/GameHUD.gd"}, "script": "res://Scripts/GameHUD.gd", "type": "Control"}], "groups": [], "name": "H<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"layer": 10}, "script": "", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"children": [{"children": [], "groups": [], "name": "ColorRect", "properties": {"color": [0.0, 0.0, 0.0, 1.0], "material": "res://Scenes/Effects/StarBackground.tres", "position": [-619.0, -324.0], "script": "res://Scripts/star_background.gd", "size": [3430.0, 1950.0], "z_as_relative": false, "z_index": -1}, "script": "res://Scripts/star_background.gd", "type": "ColorRect"}], "groups": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"layer": -1}, "script": "", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"children": [], "groups": [], "name": "HUDLayer2", "properties": {"layer": 10}, "script": "", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"children": [{"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.313017994165421, 0.313017994165421], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/planet_large.tscn::ShaderMaterial_k6nrp", "scale": [1.87482094764709, 1.87482094764709], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_large.tscn::CircleShape2D_hmd8h"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_large.tscn::CircleShape2D_njf8c"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet", "properties": {"gravity_strength": 2000.0, "position": [-980.942993164063, -154.0], "scale": [0.99734902381897, 1.0030699968338], "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.193577006459236, 0.193577006459236], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/planet_medium.tscn::ShaderMaterial_i68xm", "scale": [1.15242004394531, 1.15242004394531], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_medium.tscn::CircleShape2D_8xqrg"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_medium.tscn::CircleShape2D_hvc1f"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet3", "properties": {"gravity_strength": 1500.0, "position": [196.589996337891, 878.0], "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.123240001499653, 0.123240001499653], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_umgws", "scale": [0.744250357151031, 0.744250357151031], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_small.tscn::CircleShape2D_ngwap"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_small.tscn::CircleShape2D_s31nm"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet4", "properties": {"gravity_strength": 1000.0, "position": [854.564025878906, -167.0], "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}], "groups": [], "name": "Level", "properties": {"scale": [0.996999979019165, 1.0]}, "script": "", "type": "Node2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"rotation": -1.57079994678497, "scale": [0.453657001256943, 0.453657001256943], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites X2/Ships/spaceShips_001.png", "vframes": 2}, "script": "", "type": "Sprite2D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "Trail2D", "properties": {"begin_cap_mode": 2, "end_cap_mode": 2, "length": 15, "metadata/_custom_type_script": "uid://dgc8vmme70xjb", "points": "[(5.0, -1.0), (-88.0, -0.999996)]", "position": [-3.0, 0.0], "script": "res://addons/trail_2d/trail_2d.gd", "texture": "res://Assets/kenney_simple-space/PNG/Retina/effect_gray.png", "texture_mode": 2, "width": 13.************, "width_curve": "res://Scenes/Player.tscn::Curve_52ee3"}, "script": "res://addons/trail_2d/trail_2d.gd", "type": "Line2D"}], "groups": [], "name": "Node2D", "properties": {"position": [-39.0, -36.0]}, "script": "", "type": "Node2D"}, {"children": [{"children": [], "groups": [], "name": "Trail2D", "properties": {"begin_cap_mode": 2, "end_cap_mode": 2, "length": 15, "metadata/_custom_type_script": "uid://dgc8vmme70xjb", "points": "[(5.0, -1.0), (-88.0, -0.999996)]", "position": [-3.0, 0.0], "script": "res://addons/trail_2d/trail_2d.gd", "texture": "res://Assets/kenney_simple-space/PNG/Retina/effect_gray.png", "texture_mode": 2, "width": 13.************, "width_curve": "res://Scenes/Player.tscn::Curve_52ee3"}, "script": "res://addons/trail_2d/trail_2d.gd", "type": "Line2D"}], "groups": [], "name": "Node2D2", "properties": {"position": [-39.0, 36.0]}, "script": "", "type": "Node2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/Player.tscn::CircleShape2D_kyqiw"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "Line2D", "properties": {}, "script": "", "type": "Line2D"}, {"children": [], "groups": [], "name": "BoostParticles-Explosion", "properties": {"amount": 50, "angle_max": 360.0, "angle_min": -360.0, "color_ramp": "res://Scenes/Player.tscn::Gradient_gntrk", "direction": [0.0, 0.0], "emission_shape": 1, "emission_sphere_radius": 10.0, "emitting": false, "explosiveness": 1.0, "gravity": [0.0, 0.0], "hue_variation_max": 1.0, "hue_variation_min": -1.0, "initial_velocity_max": 100.0, "lifetime": 0.6, "local_coords": true, "one_shot": true, "position": [-33.0, 0.0], "preprocess": 0.1, "radial_accel_max": 100.0, "scale_amount_curve": "res://Scenes/Player.tscn::Curve_gntrk", "scale_amount_max": 16.0, "scale_amount_min": 5.0, "script": "res://Scenes/particle_effect.gd", "spread": 180.0}, "script": "res://Scenes/particle_effect.gd", "type": "CPUParticles2D"}, {"children": [], "groups": [], "name": "LaunchParticles", "properties": {"amount": 25, "angle_max": 360.0, "angle_min": -360.0, "color_ramp": "res://Scenes/Player.tscn::Gradient_gntrk", "direction": [0.0, 0.0], "emission_rect_extents": [1.0, 2.95499992370605], "emission_shape": 3, "emitting": false, "gravity": [-980.0, 0.0], "hue_variation_max": 1.0, "hue_variation_min": -1.0, "lifetime": 0.4, "local_coords": true, "one_shot": true, "position": [-45.0, 0.0], "preprocess": 0.1, "scale_amount_curve": "res://Scenes/Player.tscn::Curve_kyqiw", "scale_amount_max": 10.0, "scale_amount_min": 0.0, "script": "res://Scenes/particle_effect.gd"}, "script": "res://Scenes/particle_effect.gd", "type": "CPUParticles2D"}, {"children": [], "groups": [], "name": "BoostParticles", "properties": {"amount": 25, "angle_max": 360.0, "angle_min": -360.0, "color_ramp": "res://Scenes/Player.tscn::Gradient_kyqiw", "direction": [0.0, 0.0], "emission_rect_extents": [1.0, 2.95499992370605], "emission_shape": 3, "emitting": false, "gravity": [-980.0, 0.0], "hue_variation_max": 1.0, "hue_variation_min": -1.0, "lifetime": 0.5, "local_coords": true, "one_shot": true, "position": [-30.0, 0.0], "preprocess": 0.1, "scale_amount_curve": "res://Scenes/Player.tscn::Curve_kyqiw", "scale_amount_max": 15.0, "scale_amount_min": 2.0, "script": "res://Scenes/particle_effect.gd"}, "script": "res://Scenes/particle_effect.gd", "type": "CPUParticles2D"}, {"children": [], "groups": [], "name": "Camera2D", "properties": {"randomStrength": 10.0, "script": "res://Scripts/camera_shake.gd", "shakeFade": 10.0, "zoom": [0.344999998807907, 0.344999998807907]}, "script": "res://Scripts/camera_shake.gd", "type": "Camera2D"}], "groups": [], "name": "Player", "properties": {"boost_strength": 1000.0, "freeze_mode": 1, "gravity_scale": 0.0, "input_pickable": true, "launch_power": 5.0, "max_pull_distance": 200.0, "position": [1.0, -346.0], "rotation": -1.57079994678497, "script": "res://Scripts/player.gd"}, "script": "res://Scripts/player.gd", "type": "RigidBody2D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "SpaceStation022", "properties": {"position": [0.0, 84.0], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_022.png"}, "script": "", "type": "Sprite2D"}, {"children": [], "groups": [], "name": "SpaceStation018", "properties": {"position": [0.0, -59.5], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_018.png"}, "script": "", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"position": [0.5, 54.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_rh52w"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D2", "properties": {"position": [0.0, -59.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_dgjnp"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D3", "properties": {"position": [0.5, 84.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_yx0ix"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D4", "properties": {"position": [0.0, -88.0], "shape": "res://Scenes/Home.tscn::CapsuleShape2D_cxprx"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D5", "properties": {"position": [0.0, 142.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_bpuqj"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "HomeArea", "properties": {}, "script": "", "type": "Area2D"}], "groups": [], "name": "Sprite", "properties": {"texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_020.png"}, "script": "", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_umgws", "scale": [0.602626025676727, 0.602626025676727], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"disabled": true, "material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "position": [-2.0, -17.0], "shape": "res://Scenes/Planet.tscn::CircleShape2D_effgs"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D2", "properties": {"position": [0.5, 54.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_rh52w"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D3", "properties": {"position": [0.0, -59.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_dgjnp"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D4", "properties": {"position": [0.5, 84.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_yx0ix"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D5", "properties": {"position": [0.0, -88.0], "shape": "res://Scenes/Home.tscn::CapsuleShape2D_cxprx"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D6", "properties": {"position": [0.0, 142.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_bpuqj"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet2", "properties": {"gravity_strength": 2000.0, "script": "res://Scripts/HomePlanet.gd"}, "script": "res://Scripts/HomePlanet.gd", "type": "Area2D"}], "groups": [], "name": "Game", "properties": {"script": "res://Scripts/GameController.gd"}, "script": "res://Scripts/GameController.gd", "type": "Node2D"}}, {"path": "res:///Scenes/Home.tscn", "root_node": {"children": [{"children": [{"children": [], "groups": [], "name": "SpaceStation022", "properties": {"position": [0.0, 84.0], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_022.png"}, "script": "", "type": "Sprite2D"}, {"children": [], "groups": [], "name": "SpaceStation018", "properties": {"position": [0.0, -59.5], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_018.png"}, "script": "", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"position": [0.5, 54.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_rh52w"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D2", "properties": {"position": [0.0, -59.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_dgjnp"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D3", "properties": {"position": [0.5, 84.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_yx0ix"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D4", "properties": {"position": [0.0, -88.0], "shape": "res://Scenes/Home.tscn::CapsuleShape2D_cxprx"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D5", "properties": {"position": [0.0, 142.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_bpuqj"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "HomeArea", "properties": {}, "script": "", "type": "Area2D"}], "groups": [], "name": "Sprite", "properties": {"texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_020.png"}, "script": "", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_umgws", "scale": [0.602626025676727, 0.602626025676727], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"disabled": true, "material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "position": [-2.0, -17.0], "shape": "res://Scenes/Planet.tscn::CircleShape2D_effgs"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D2", "properties": {"position": [0.5, 54.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_rh52w"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D3", "properties": {"position": [0.0, -59.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_dgjnp"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D4", "properties": {"position": [0.5, 84.0], "shape": "res://Scenes/Home.tscn::RectangleShape2D_yx0ix"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D5", "properties": {"position": [0.0, -88.0], "shape": "res://Scenes/Home.tscn::CapsuleShape2D_cxprx"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "CollisionShape2D6", "properties": {"position": [0.0, 142.5], "shape": "res://Scenes/Home.tscn::RectangleShape2D_bpuqj"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet", "properties": {"StationSprite": "res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_020.png", "gravity_strength": 2000.0, "script": "res://Scripts/HomePlanet.gd"}, "script": "res://Scripts/HomePlanet.gd", "type": "Area2D"}}, {"path": "res:///Scenes/level.tscn", "root_node": {"children": [{"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.313017994165421, 0.313017994165421], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/planet_large.tscn::ShaderMaterial_k6nrp", "scale": [1.87482094764709, 1.87482094764709], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_large.tscn::CircleShape2D_hmd8h"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_large.tscn::CircleShape2D_njf8c"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet", "properties": {"gravity_strength": 2000.0, "position": [-980.942993164063, -154.0], "scale": [0.99734902381897, 1.0030699968338], "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.193577006459236, 0.193577006459236], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/planet_medium.tscn::ShaderMaterial_i68xm", "scale": [1.15242004394531, 1.15242004394531], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_medium.tscn::CircleShape2D_8xqrg"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_medium.tscn::CircleShape2D_hvc1f"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet3", "properties": {"gravity_strength": 1500.0, "position": [196.589996337891, 878.0], "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.123240001499653, 0.123240001499653], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_umgws", "scale": [0.744250357151031, 0.744250357151031], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_small.tscn::CircleShape2D_ngwap"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_small.tscn::CircleShape2D_s31nm"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet4", "properties": {"gravity_strength": 1000.0, "position": [854.564025878906, -167.0], "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}], "groups": [], "name": "Level", "properties": {"active": false, "scale": [0.996999979019165, 1.0], "script": "res://Scripts/tools/level.gd"}, "script": "res://Scripts/tools/level.gd", "type": "Node2D"}}, {"path": "res:///Scenes/Planet.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "script": "res://Scripts/rand_sprite.gd"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_umgws", "scale": [0.602626025676727, 0.602626025676727], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/Planet.tscn::CircleShape2D_effgs"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet2", "properties": {"gravity_strength": 6000.0, "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}}, {"path": "res:///Scenes/planet_large.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.313017994165421, 0.313017994165421], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/planet_large.tscn::ShaderMaterial_k6nrp", "scale": [1.87482094764709, 1.87482094764709], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_large.tscn::CircleShape2D_hmd8h"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_large.tscn::CircleShape2D_njf8c"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet", "properties": {"gravity_strength": 2000.0, "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}}, {"path": "res:///Scenes/planet_medium.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.193577006459236, 0.193577006459236], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/planet_medium.tscn::ShaderMaterial_i68xm", "scale": [1.15242004394531, 1.15242004394531], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_medium.tscn::CircleShape2D_8xqrg"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_medium.tscn::CircleShape2D_hvc1f"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet3", "properties": {"gravity_strength": 1500.0, "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}}, {"path": "res:///Scenes/planet_small.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite", "properties": {"PlanetSprites": ["<CompressedTexture2D#-9223369723847031244>", "<CompressedTexture2D#-9223369723444378054>", "<CompressedTexture2D#-9223369723024947647>", "<CompressedTexture2D#-9223352465225593653>", "<CompressedTexture2D#-9223352464990712628>", "<CompressedTexture2D#-9223352464772608826>", "<CompressedTexture2D#-9223352464554515291>", "<CompressedTexture2D#-9223352464336403089>", "<CompressedTexture2D#-9223352464118299247>", "<CompressedTexture2D#-9223352463900195433>"], "scale": [0.123240001499653, 0.123240001499653], "script": "res://Scripts/rand_sprite.gd", "texture": "res://Assets/kenney_planets/Planets/planet00.png"}, "script": "res://Scripts/rand_sprite.gd", "type": "Sprite2D"}, {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_umgws", "scale": [0.744250357151031, 0.744250357151031], "script": "res://Scripts/tools/planet_atmo.gd", "texture": "res://Assets/Circle.png"}, "script": "res://Scripts/tools/planet_atmo.gd", "type": "Sprite2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"material": "res://Scenes/Planet.tscn::ShaderMaterial_aogve", "shape": "res://Scenes/planet_small.tscn::CircleShape2D_ngwap"}, "script": "", "type": "CollisionShape2D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/planet_small.tscn::CircleShape2D_s31nm"}, "script": "", "type": "CollisionShape2D"}], "groups": [], "name": "AnimatableBody2D", "properties": {}, "script": "", "type": "AnimatableBody2D"}, {"children": [], "groups": [], "name": "Node2D", "properties": {"OrbitRangeMax": 0.0, "OrbitRangeMin": 0.0, "OrbitSpeedMax": 1.5, "OrbitSpeedMin": 0.6, "script": "res://Scenes/CollectableSpawner.gd"}, "script": "res://Scenes/CollectableSpawner.gd", "type": "Node2D"}], "groups": [], "name": "Planet3", "properties": {"gravity_strength": 1000.0, "script": "res://Scripts/planet.gd"}, "script": "res://Scripts/planet.gd", "type": "Area2D"}}, {"path": "res:///Scenes/Player.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Sprite2D", "properties": {"rotation": -1.57079994678497, "scale": [0.453657001256943, 0.453657001256943], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites X2/Ships/spaceShips_001.png", "vframes": 2}, "script": "", "type": "Sprite2D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "Trail2D", "properties": {"begin_cap_mode": 2, "end_cap_mode": 2, "length": 15, "metadata/_custom_type_script": "uid://dgc8vmme70xjb", "points": "[(5.0, -1.0), (-88.0, -0.999996)]", "position": [-3.0, 0.0], "script": "res://addons/trail_2d/trail_2d.gd", "texture": "res://Assets/kenney_simple-space/PNG/Retina/effect_gray.png", "texture_mode": 2, "width": 13.************, "width_curve": "res://Scenes/Player.tscn::Curve_52ee3"}, "script": "res://addons/trail_2d/trail_2d.gd", "type": "Line2D"}], "groups": [], "name": "Node2D", "properties": {"position": [-39.0, -36.0]}, "script": "", "type": "Node2D"}, {"children": [{"children": [], "groups": [], "name": "Trail2D", "properties": {"begin_cap_mode": 2, "end_cap_mode": 2, "length": 15, "metadata/_custom_type_script": "uid://dgc8vmme70xjb", "points": "[(5.0, -1.0), (-88.0, -0.999996)]", "position": [-3.0, 0.0], "script": "res://addons/trail_2d/trail_2d.gd", "texture": "res://Assets/kenney_simple-space/PNG/Retina/effect_gray.png", "texture_mode": 2, "width": 13.************, "width_curve": "res://Scenes/Player.tscn::Curve_52ee3"}, "script": "res://addons/trail_2d/trail_2d.gd", "type": "Line2D"}], "groups": [], "name": "Node2D2", "properties": {"position": [-39.0, 36.0]}, "script": "", "type": "Node2D"}], "groups": [], "name": "CollisionShape2D", "properties": {"shape": "res://Scenes/Player.tscn::CircleShape2D_kyqiw"}, "script": "", "type": "CollisionShape2D"}, {"children": [], "groups": [], "name": "Line2D", "properties": {}, "script": "", "type": "Line2D"}, {"children": [], "groups": [], "name": "BoostParticles-Explosion", "properties": {"amount": 50, "angle_max": 360.0, "angle_min": -360.0, "color_ramp": "res://Scenes/Player.tscn::Gradient_gntrk", "direction": [0.0, 0.0], "emission_shape": 1, "emission_sphere_radius": 10.0, "emitting": false, "explosiveness": 1.0, "gravity": [0.0, 0.0], "hue_variation_max": 1.0, "hue_variation_min": -1.0, "initial_velocity_max": 100.0, "lifetime": 0.6, "local_coords": true, "one_shot": true, "position": [-33.0, 0.0], "preprocess": 0.1, "radial_accel_max": 100.0, "scale_amount_curve": "res://Scenes/Player.tscn::Curve_gntrk", "scale_amount_max": 16.0, "scale_amount_min": 5.0, "script": "res://Scenes/particle_effect.gd", "spread": 180.0}, "script": "res://Scenes/particle_effect.gd", "type": "CPUParticles2D"}, {"children": [], "groups": [], "name": "LaunchParticles", "properties": {"amount": 25, "angle_max": 360.0, "angle_min": -360.0, "color_ramp": "res://Scenes/Player.tscn::Gradient_gntrk", "direction": [0.0, 0.0], "emission_rect_extents": [1.0, 2.95499992370605], "emission_shape": 3, "emitting": false, "gravity": [-980.0, 0.0], "hue_variation_max": 1.0, "hue_variation_min": -1.0, "lifetime": 0.4, "local_coords": true, "one_shot": true, "position": [-45.0, 0.0], "preprocess": 0.1, "scale_amount_curve": "res://Scenes/Player.tscn::Curve_kyqiw", "scale_amount_max": 10.0, "scale_amount_min": 0.0, "script": "res://Scenes/particle_effect.gd"}, "script": "res://Scenes/particle_effect.gd", "type": "CPUParticles2D"}, {"children": [], "groups": [], "name": "BoostParticles", "properties": {"amount": 25, "angle_max": 360.0, "angle_min": -360.0, "color_ramp": "res://Scenes/Player.tscn::Gradient_kyqiw", "direction": [0.0, 0.0], "emission_rect_extents": [1.0, 2.95499992370605], "emission_shape": 3, "emitting": false, "gravity": [-980.0, 0.0], "hue_variation_max": 1.0, "hue_variation_min": -1.0, "lifetime": 0.5, "local_coords": true, "one_shot": true, "position": [-30.0, 0.0], "preprocess": 0.1, "scale_amount_curve": "res://Scenes/Player.tscn::Curve_kyqiw", "scale_amount_max": 15.0, "scale_amount_min": 2.0, "script": "res://Scenes/particle_effect.gd"}, "script": "res://Scenes/particle_effect.gd", "type": "CPUParticles2D"}, {"children": [], "groups": [], "name": "Camera2D", "properties": {"randomStrength": 10.0, "script": "res://Scripts/camera_shake.gd", "shakeFade": 10.0, "zoom": [0.344999998807907, 0.344999998807907]}, "script": "res://Scripts/camera_shake.gd", "type": "Camera2D"}], "groups": [], "name": "Player", "properties": {"boost_strength": 1000.0, "freeze_mode": 1, "gravity_scale": 0.0, "input_pickable": true, "launch_power": 10.0, "max_pull_distance": 200.0, "script": "res://Scripts/player.gd"}, "script": "res://Scripts/player.gd", "type": "RigidBody2D"}}, {"path": "res:///Scenes/UI/CollectionNotification.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "NotificationLabel", "properties": {"anchors_preset": 15, "horizontal_alignment": 2, "label_settings": "res://Scenes/UI/CollectionNotification.tscn::LabelSettings_1", "layout_mode": 1, "position": [-0.5, -11.5], "size": [1.0, 23.0], "text": "+10 Star", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "FadeTimer", "properties": {"one_shot": true, "wait_time": 2.0}, "script": "", "type": "Timer"}], "groups": [], "name": "CollectionNotification", "properties": {"script": "res://Scripts/CollectionNotification.gd", "size": [200.0, 40.0]}, "script": "res://Scripts/CollectionNotification.gd", "type": "Control"}}, {"path": "res:///Scenes/UI/Compass.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "expand_mode": 1, "layout_mode": 1, "stretch_mode": 5, "texture": "res://Assets/Circle.png"}, "script": "", "type": "TextureRect"}, {"children": [{"children": [], "groups": [], "name": "HomeIcon", "properties": {"anchors_preset": 8, "expand_mode": 1, "layout_mode": 1, "position": [-8.0, -8.0], "size": [16.0, 16.0], "stretch_mode": 5, "texture": "res://Assets/kenney_simple-space/PNG/Default/station_A.png"}, "script": "", "type": "TextureRect"}, {"children": [], "groups": [], "name": "PlanetIcons", "properties": {"anchors_preset": 15, "layout_mode": 1}, "script": "", "type": "Control"}], "groups": [], "name": "CompassCenter", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-55.0, -55.0], "size": [110.0, 110.0]}, "script": "", "type": "Control"}], "groups": [], "name": "<PERSON>mp<PERSON>", "properties": {"anchors_preset": 1, "map_radius": 50.0, "map_scale": 0.01, "position": [-120.0, 10.0], "script": "res://Scripts/Compass.gd", "size": [110.0, 110.0]}, "script": "res://Scripts/Compass.gd", "type": "Control"}}, {"path": "res:///Scenes/UI/GameHUD.tscn", "root_node": {"children": [{"children": [{"children": [], "groups": [], "name": "ScoreLabel", "properties": {"label_settings": "res://Scenes/UI/GameHUD.tscn::LabelSettings_4y6w4", "layout_mode": 2, "text": "Score: 0"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "BoostsLabel", "properties": {"label_settings": "res://Scenes/UI/GameHUD.tscn::LabelSettings_4y6w4", "layout_mode": 2, "text": "Boosts: 1"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "BoostPowerLabel", "properties": {"label_settings": "res://Scenes/UI/GameHUD.tscn::LabelSettings_4y6w4", "layout_mode": 2, "text": "Launch Power: 0%"}, "script": "", "type": "Label"}], "groups": [], "name": "VBoxContainer", "properties": {"layout_mode": 0, "position": [15.0, 15.0], "size": [250.0, 89.0]}, "script": "", "type": "VBoxContainer"}, {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "expand_mode": 1, "layout_mode": 1, "stretch_mode": 5, "texture": "res://Assets/Circle.png"}, "script": "", "type": "TextureRect"}, {"children": [{"children": [], "groups": [], "name": "HomeIcon", "properties": {"anchors_preset": 8, "expand_mode": 1, "layout_mode": 1, "position": [-8.0, -8.0], "size": [16.0, 16.0], "stretch_mode": 5, "texture": "res://Assets/kenney_simple-space/PNG/Default/station_A.png"}, "script": "", "type": "TextureRect"}, {"children": [], "groups": [], "name": "PlanetIcons", "properties": {"anchors_preset": 15, "layout_mode": 1}, "script": "", "type": "Control"}], "groups": [], "name": "CompassCenter", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-55.0, -55.0], "size": [110.0, 110.0]}, "script": "", "type": "Control"}], "groups": [], "name": "<PERSON>mp<PERSON>", "properties": {"anchors_preset": 1, "layout_mode": 1, "map_radius": 50.0, "map_scale": 0.01, "position": [-120.0, 10.0], "script": "res://Scripts/Compass.gd", "size": [110.0, 110.0]}, "script": "res://Scripts/Compass.gd", "type": "Control"}, {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "layout_mode": 1, "theme_override_styles/panel": "res://Scenes/UI/ObjectivesPanel.tscn::StyleBoxFlat_1"}, "script": "", "type": "Panel"}, {"children": [{"children": [], "groups": [], "name": "TitleLabel", "properties": {"horizontal_alignment": 1, "label_settings": "res://Scenes/UI/ObjectivesPanel.tscn::LabelSettings_title", "layout_mode": 2, "text": "OBJECTIVES"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "HSeparator", "properties": {"layout_mode": 2}, "script": "", "type": "HSeparator"}], "groups": [], "name": "VBoxContainer", "properties": {"anchors_preset": 15, "layout_mode": 1}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "ObjectivesPanel", "properties": {"layout_mode": 0, "position": [15.0, 120.0], "script": "res://Scripts/ObjectivesPanel.gd", "size": [250.0, 300.0], "visible": false}, "script": "res://Scripts/ObjectivesPanel.gd", "type": "Control"}, {"children": [], "groups": [], "name": "NotificationContainer", "properties": {"anchors_preset": 3, "layout_mode": 1, "position": [-250.0, -100.0], "size": [250.0, 100.0]}, "script": "", "type": "Control"}], "groups": [], "name": "GameHUD", "properties": {"anchors_preset": 15, "mouse_filter": 2, "script": "res://Scripts/GameHUD.gd"}, "script": "res://Scripts/GameHUD.gd", "type": "Control"}}, {"path": "res:///Scenes/UI/LoseScreen.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "color": [0.200000002980232, 0.0500000007450581, 0.0500000007450581, 1.0], "layout_mode": 1, "material": "res://Scenes/Effects/StarBackground_Menu.tres"}, "script": "", "type": "ColorRect"}, {"children": [{"children": [], "groups": [], "name": "Lose<PERSON>itle", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "text": "MISSION FAILED", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "Spacer1", "properties": {"custom_minimum_size": [0.0, 20.0], "layout_mode": 2}, "script": "", "type": "Control"}, {"children": [], "groups": [], "name": "LoseMessage", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "text": "You drifted too far from home...", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "Spacer2", "properties": {"custom_minimum_size": [0.0, 40.0], "layout_mode": 2}, "script": "", "type": "Control"}, {"children": [], "groups": [], "name": "RestartButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "TRY AGAIN"}, "script": "", "type": "<PERSON><PERSON>"}, {"children": [], "groups": [], "name": "MenuButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "MAIN MENU"}, "script": "", "type": "<PERSON><PERSON>"}], "groups": [], "name": "VBoxContainer", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-150.0, -120.0], "size": [300.0, 240.0]}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "LoseScreen", "properties": {"anchors_preset": 15, "script": "res://Scripts/LoseScreen.gd", "theme": "res://Scenes/UI/Themes/UI_Theme.tres"}, "script": "res://Scripts/LoseScreen.gd", "type": "Control"}}, {"path": "res:///Scenes/UI/MainMenu.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "color": [0.100000001490116, 0.100000001490116, 0.200000002980232, 1.0], "layout_mode": 1, "material": "res://Scenes/Effects/StarBackground_Menu.tres"}, "script": "", "type": "ColorRect"}, {"children": [{"children": [], "groups": [], "name": "GameTitle", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "text": "SPACE LOOP", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "Spacer1", "properties": {"custom_minimum_size": [0.0, 40.0], "layout_mode": 2}, "script": "", "type": "Control"}, {"children": [], "groups": [], "name": "StartButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "START GAME"}, "script": "", "type": "<PERSON><PERSON>"}, {"children": [], "groups": [], "name": "SettingsButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "SETTINGS"}, "script": "", "type": "<PERSON><PERSON>"}, {"children": [], "groups": [], "name": "ExitButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "EXIT"}, "script": "", "type": "<PERSON><PERSON>"}], "groups": [], "name": "VBoxContainer", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-100.0, -120.0], "size": [200.0, 240.0]}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "MainMenu", "properties": {"anchors_preset": 15, "script": "res://Scripts/MainMenu.gd", "theme": "res://Scenes/UI/Themes/UI_Theme.tres"}, "script": "res://Scripts/MainMenu.gd", "type": "Control"}}, {"path": "res:///Scenes/UI/MusicManager.tscn", "root_node": {"children": [], "groups": [], "name": "AudioManager", "properties": {"audios_2d": [], "audios_3d": [], "audios_omni": ["<Resource#-9223326675121387904>"], "metadata/_custom_type_script": "uid://jdiu05rj7h3d", "script": "res://addons/audio_manager/audio_manager.gd"}, "script": "res://addons/audio_manager/audio_manager.gd", "type": "Node"}}, {"path": "res:///Scenes/UI/ObjectivesPanel.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "layout_mode": 1, "theme_override_styles/panel": "res://Scenes/UI/ObjectivesPanel.tscn::StyleBoxFlat_1"}, "script": "", "type": "Panel"}, {"children": [{"children": [], "groups": [], "name": "TitleLabel", "properties": {"horizontal_alignment": 1, "label_settings": "res://Scenes/UI/ObjectivesPanel.tscn::LabelSettings_title", "layout_mode": 2, "text": "OBJECTIVES"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "HSeparator", "properties": {"layout_mode": 2}, "script": "", "type": "HSeparator"}], "groups": [], "name": "VBoxContainer", "properties": {"anchors_preset": 15, "layout_mode": 1}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "ObjectivesPanel", "properties": {"script": "res://Scripts/ObjectivesPanel.gd", "size": [250.0, 300.0]}, "script": "res://Scripts/ObjectivesPanel.gd", "type": "Control"}}, {"path": "res:///Scenes/UI/Settings.tscn", "root_node": {"children": [{"children": [{"children": [{"children": [], "groups": [], "name": "SettingsTitle", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "size_flags_horizontal": 0, "size_flags_vertical": 0, "text": "SETTINGS", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [{"children": [], "groups": [], "name": "Spacer1", "properties": {"custom_minimum_size": [0.0, 30.0], "layout_mode": 2}, "script": "", "type": "Control"}, {"children": [{"children": [], "groups": [], "name": "CheckBox", "properties": {"layout_mode": 2}, "script": "", "type": "CheckBox"}, {"children": [], "groups": [], "name": "Label", "properties": {"custom_minimum_size": [100.0, 0.0], "layout_mode": 2, "size_flags_horizontal": 0, "text": "- Master"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "HSlider2", "properties": {"layout_mode": 2, "max_value": 3.5, "size_flags_horizontal": 3, "size_flags_vertical": 1, "step": 0.1}, "script": "", "type": "HSlider"}], "groups": [], "name": "MasterVolume", "properties": {"busName": "Master", "layout_mode": 2, "script": "res://Scenes/UI/volume_control.gd", "size": [376.0, 32.0]}, "script": "res://Scenes/UI/volume_control.gd", "type": "HBoxContainer"}, {"children": [{"children": [], "groups": [], "name": "CheckBox", "properties": {"layout_mode": 2}, "script": "", "type": "CheckBox"}, {"children": [], "groups": [], "name": "Label", "properties": {"custom_minimum_size": [100.0, 0.0], "layout_mode": 2, "size_flags_horizontal": 0, "text": "- Master"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "HSlider2", "properties": {"layout_mode": 2, "max_value": 3.5, "size_flags_horizontal": 3, "size_flags_vertical": 1, "step": 0.1}, "script": "", "type": "HSlider"}], "groups": [], "name": "Music", "properties": {"busName": "Music", "layout_mode": 2, "script": "res://Scenes/UI/volume_control.gd", "size": [376.0, 32.0]}, "script": "res://Scenes/UI/volume_control.gd", "type": "HBoxContainer"}, {"children": [], "groups": [], "name": "ShipColorLabel", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "text": "Ship Color:", "visible": false}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "Spacer2", "properties": {"custom_minimum_size": [0.0, 20.0], "layout_mode": 2, "visible": false}, "script": "", "type": "Control"}, {"children": [{"children": [], "groups": [], "name": "ExampleShip", "properties": {"rotation": 4.71238994598389, "scale": [0.600000023841858, 0.600000023841858], "texture": "res://Assets/kenney_space-shooter-extension/PNG/Sprites X2/Ships/spaceShips_001.png", "vframes": 2}, "script": "", "type": "Sprite2D"}], "groups": [], "name": "ExampleShipContainer", "properties": {"custom_minimum_size": [0.0, 80.0], "layout_mode": 2, "visible": false}, "script": "", "type": "CenterContainer"}, {"children": [], "groups": [], "name": "Spacer3", "properties": {"custom_minimum_size": [0.0, 20.0], "layout_mode": 2, "visible": false}, "script": "", "type": "Control"}, {"children": [{"children": [], "groups": [], "name": "ColorSliderLabel", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "text": "Drag to change color:"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "ColorSlider", "properties": {"custom_minimum_size": [300.0, 30.0], "layout_mode": 2, "max_value": 1.0, "step": 0.01}, "script": "", "type": "HSlider"}], "groups": [], "name": "ColorSliderContainer", "properties": {"layout_mode": 2, "visible": false}, "script": "", "type": "VBoxContainer"}, {"children": [], "groups": [], "name": "Spacer4", "properties": {"custom_minimum_size": [0.0, 30.0], "layout_mode": 2}, "script": "", "type": "Control"}, {"children": [], "groups": [], "name": "BackButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "BACK TO MENU"}, "script": "", "type": "<PERSON><PERSON>"}], "groups": [], "name": "VBoxContainer", "properties": {"layout_mode": 2}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "Panel", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-219.0, -135.5], "size": [438.0, 271.0]}, "script": "", "type": "PanelContainer"}], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "color": [0.409999996423721, 0.409999996423721, 0.819999992847443, 1.0], "layout_mode": 1, "material": "res://Scenes/Effects/StarBackground_Menu.tres", "script": "res://Scenes/UI/StarBackground_Menu.gd"}, "script": "res://Scenes/UI/StarBackground_Menu.gd", "type": "ColorRect"}], "groups": [], "name": "Settings", "properties": {"anchors_preset": 15, "script": "res://Scripts/Settings.gd", "theme": "res://Scenes/UI/Themes/UI_Theme.tres"}, "script": "res://Scripts/Settings.gd", "type": "Control"}}, {"path": "res:///Scenes/UI/Shop.tscn", "root_node": {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "groups": [], "name": "TitleLabel", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "size_flags_horizontal": 3, "text": "SPACE SHOP"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "ScoreLabel", "properties": {"layout_mode": 2, "text": "Score: 0"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "CloseButton", "properties": {"custom_minimum_size": [80.0, 30.0], "layout_mode": 2, "text": "CLOSE"}, "script": "", "type": "<PERSON><PERSON>"}], "groups": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"layout_mode": 2}, "script": "", "type": "HBoxContainer"}, {"children": [], "groups": [], "name": "HSeparator", "properties": {"layout_mode": 2}, "script": "", "type": "HSeparator"}, {"children": [{"children": [], "groups": [], "name": "ItemsContainer", "properties": {"layout_mode": 2, "size_flags_horizontal": 3}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "<PERSON>rollC<PERSON>r", "properties": {"layout_mode": 2, "size_flags_vertical": 3}, "script": "", "type": "<PERSON>rollC<PERSON>r"}], "groups": [], "name": "VBoxContainer", "properties": {"anchors_preset": 15, "layout_mode": 1}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "ShopPanel", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-300.0, -250.0], "size": [600.0, 500.0], "theme_override_styles/panel": "res://Scenes/UI/Shop.tscn::StyleBoxFlat_1"}, "script": "", "type": "Panel"}], "groups": [], "name": "Control", "properties": {"anchors_preset": 15, "theme": "res://Scenes/UI/Themes/UI_Theme.tres"}, "script": "", "type": "Control"}], "groups": [], "name": "ShopManager", "properties": {"script": "res://Scripts/ShopManager.gd"}, "script": "res://Scripts/ShopManager.gd", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"path": "res:///Scenes/UI/ShopPrompt.tscn", "root_node": {"children": [{"children": [{"children": [], "groups": [], "name": "Prompt<PERSON><PERSON><PERSON>", "properties": {"anchors_preset": 15, "horizontal_alignment": 1, "layout_mode": 1, "position": [-0.5, -11.5], "size": [1.0, 23.0], "text": "E", "theme_override_colors/font_color": [1.0, 1.0, 1.0, 1.0], "theme_override_font_sizes/font_size": 24, "theme_override_styles/normal": "res://Scenes/UI/ShopPrompt.tscn::StyleBoxFlat_1", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "AnimationPlayer", "properties": {"libraries": {"": "<AnimationLibrary#-9223318855160325310>"}}, "script": "", "type": "AnimationPlayer"}], "groups": [], "name": "Control", "properties": {"custom_minimum_size": [40.0, 40.0], "modulate": [1.0, 1.0, 1.0, 0.800000011920929], "size": [40.0, 40.0], "theme": "res://Scenes/UI/Themes/UI_Theme.tres"}, "script": "", "type": "Control"}], "groups": [], "name": "ShopPrompt", "properties": {"script": "res://Scripts/ShopPrompt.gd"}, "script": "res://Scripts/ShopPrompt.gd", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"path": "res:///Scenes/UI/volume_control.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "CheckBox", "properties": {"layout_mode": 2}, "script": "", "type": "CheckBox"}, {"children": [], "groups": [], "name": "Label", "properties": {"custom_minimum_size": [100.0, 0.0], "layout_mode": 2, "size_flags_horizontal": 0, "text": "- Master"}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "HSlider2", "properties": {"layout_mode": 2, "max_value": 3.5, "size_flags_horizontal": 3, "size_flags_vertical": 1, "step": 0.1}, "script": "", "type": "HSlider"}], "groups": [], "name": "MasterVolume", "properties": {"busName": "", "script": "res://Scenes/UI/volume_control.gd", "size": [376.0, 32.0]}, "script": "res://Scenes/UI/volume_control.gd", "type": "HBoxContainer"}}, {"path": "res:///Scenes/UI/WinScreen.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "Background", "properties": {"anchors_preset": 15, "color": [0.0500000007450581, 0.200000002980232, 0.0500000007450581, 1.0], "layout_mode": 1}, "script": "", "type": "ColorRect"}, {"children": [{"children": [], "groups": [], "name": "WinTitle", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "text": "MISSION COMPLETE!", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "Spacer1", "properties": {"custom_minimum_size": [0.0, 20.0], "layout_mode": 2}, "script": "", "type": "Control"}, {"children": [], "groups": [], "name": "WinMessage", "properties": {"horizontal_alignment": 1, "layout_mode": 2, "text": "You collected all the collectables!", "vertical_alignment": 1}, "script": "", "type": "Label"}, {"children": [], "groups": [], "name": "Spacer2", "properties": {"custom_minimum_size": [0.0, 40.0], "layout_mode": 2}, "script": "", "type": "Control"}, {"children": [], "groups": [], "name": "RestartButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "PLAY AGAIN"}, "script": "", "type": "<PERSON><PERSON>"}, {"children": [], "groups": [], "name": "MenuButton", "properties": {"custom_minimum_size": [200.0, 50.0], "layout_mode": 2, "text": "MAIN MENU"}, "script": "", "type": "<PERSON><PERSON>"}], "groups": [], "name": "VBoxContainer", "properties": {"anchors_preset": 8, "layout_mode": 1, "position": [-150.0, -120.0], "size": [300.0, 240.0]}, "script": "", "type": "VBoxContainer"}], "groups": [], "name": "WinScreen", "properties": {"anchors_preset": 15, "script": "res://Scripts/WinScreen.gd", "theme": "res://Scenes/UI/Themes/UI_Theme.tres"}, "script": "res://Scripts/WinScreen.gd", "type": "Control"}}], "scripts": [{"content": "@tool\nextends RefCounted\n\nfunc export_project_structure() -> bool:\n\tvar output = {\n\t\t\"project_name\": ProjectSettings.get_setting(\"application/config/name\", \"Unknown\"),\n\t\t\"generated_timestamp\": Time.get_datetime_string_from_system(),\n\t\t\"scenes\": [],\n\t\t\"scripts\": [],\n\t\t\"autoloads\": {},\n\t\t\"project_settings\": get_relevant_project_settings()\n\t}\n\t\n\t# Get all scene files\n\tvar scene_files = find_files_recursive(\"res://\", \"tscn\")\n\tvar total_files = scene_files.size()\n\t\n\tprint(\"AI Context Generator: Found %d scenes to process...\" % total_files)\n\t\n\t# Process each scene\n\tfor i in range(scene_files.size()):\n\t\tvar scene_path = scene_files[i]\n\t\t\n\t\tprint(\"AI Context Generator: Processing scene %d/%d: %s\" % [i+1, total_files, scene_path.get_file()])\n\t\t\n\t\tvar scene_data = parse_scene_structure(scene_path)\n\t\tif scene_data:\n\t\t\toutput.scenes.append(scene_data)\n\t\n\t# Get all script files\n\tvar script_files = find_files_recursive(\"res://\", \"gd\")\n\tscript_files.append_array(find_files_recursive(\"res://\", \"cs\"))\n\t\n\tprint(\"AI Context Generator: Processing %d scripts...\" % script_files.size())\n\t\n\t# Process each script\n\tfor i in range(script_files.size()):\n\t\tvar script_path = script_files[i]\n\t\t\n\t\tvar script_data = extract_script_info(script_path)\n\t\tif script_data:\n\t\t\toutput.scripts.append(script_data)\n\t\n\t# Get autoloads\n\toutput.autoloads = get_autoloads()\n\t\n\t# Save to file\n\tvar json_string = JSON.stringify(output, \"\\t\")\n\tvar file = FileAccess.open(\"res://ai_context_export.json\", FileAccess.WRITE)\n\t\n\tif file:\n\t\tfile.store_string(json_string)\n\t\tfile.close()\n\t\t\n\t\t# Copy to clipboard\n\t\tDisplayServer.clipboard_set(json_string)\n\t\t\n\t\tprint(\"AI Context Generator: Successfully generated ai_context_export.json\")\n\t\tprint(\"AI Context Generator: JSON data copied to clipboard!\")\n\t\treturn true\n\telse:\n\t\tprint(\"AI Context Generator: Failed to write export file\")\n\t\treturn false\n\nfunc find_files_recursive(path: String, extension: String) -> Array[String]:\n\tvar files: Array[String] = []\n\tvar dir = DirAccess.open(path)\n\t\n\tif dir:\n\t\tdir.list_dir_begin()\n\t\tvar file_name = dir.get_next()\n\t\t\n\t\twhile file_name != \"\":\n\t\t\tvar full_path = path + \"/\" + file_name\n\t\t\t\n\t\t\tif dir.current_is_dir() and not file_name.begins_with(\".\"):\n\t\t\t\tfiles.append_array(find_files_recursive(full_path, extension))\n\t\t\telif file_name.get_extension() == extension:\n\t\t\t\tfiles.append(full_path)\n\t\t\t\n\t\t\tfile_name = dir.get_next()\n\t\t\n\t\tdir.list_dir_end()\n\t\n\treturn files\n\nfunc parse_scene_structure(scene_path: String) -> Dictionary:\n\tvar scene = load(scene_path) as PackedScene\n\tif not scene:\n\t\treturn {}\n\t\n\tvar instance = scene.instantiate()\n\tif not instance:\n\t\treturn {}\n\t\n\tvar scene_data = {\n\t\t\"path\": scene_path,\n\t\t\"root_node\": parse_node_recursive(instance)\n\t}\n\t\n\tinstance.queue_free()\n\treturn scene_data\n\nfunc parse_node_recursive(node: Node) -> Dictionary:\n\tvar node_data = {\n\t\t\"name\": node.name,\n\t\t\"type\": node.get_class(),\n\t\t\"script\": \"\",\n\t\t\"properties\": {},\n\t\t\"groups\": [],\n\t\t\"children\": []\n\t}\n\t\n\t# Get script path if attached\n\tif node.get_script():\n\t\tnode_data.script = node.get_script().resource_path\n\t\n\t# Get non-default properties\n\tnode_data.properties = get_non_default_properties(node)\n\t\n\t# Get groups\n\tfor group in node.get_groups():\n\t\tnode_data.groups.append(group)\n\t\n\t# Process children\n\tfor child in node.get_children():\n\t\tnode_data.children.append(parse_node_recursive(child))\n\t\n\treturn node_data\n\nfunc get_non_default_properties(node: Node) -> Dictionary:\n\tvar properties = {}\n\t\n\t# Create a default instance of the same type for comparison\n\tvar default_node = null\n\tvar node_class = node.get_class()\n\t\n\t# Try to create default instance\n\tif ClassDB.can_instantiate(node_class):\n\t\tdefault_node = ClassDB.instantiate(node_class)\n\t\n\tif not default_node:\n\t\treturn properties\n\t\n\t# Get all properties\n\tvar property_list = node.get_property_list()\n\t\n\tfor prop in property_list:\n\t\t# Only include editor-visible properties that aren't built-in engine properties\n\t\tif (prop.usage & PROPERTY_USAGE_EDITOR) and not prop.name.begins_with(\"_\"):\n\t\t\tvar current_value = node.get(prop.name)\n\t\t\tvar default_value = default_node.get(prop.name)\n\t\t\t\n\t\t\t# Compare values (handle different types appropriately)\n\t\t\tif not values_equal(current_value, default_value):\n\t\t\t\tproperties[prop.name] = serialize_value(current_value)\n\t\n\tdefault_node.queue_free()\n\treturn properties\n\nfunc values_equal(a, b) -> bool:\n\t# Handle Vector2, Vector3, Color, etc.\n\tif typeof(a) != typeof(b):\n\t\treturn false\n\t\n\tif a is Vector2 or a is Vector3 or a is Vector4:\n\t\treturn a.is_equal_approx(b)\n\telif a is Color:\n\t\treturn a.is_equal_approx(b)\n\telse:\n\t\treturn a == b\n\nfunc serialize_value(value):\n\t# Convert complex types to serializable formats\n\tif value is Vector2:\n\t\treturn [value.x, value.y]\n\telif value is Vector3:\n\t\treturn [value.x, value.y, value.z]\n\telif value is Vector4:\n\t\treturn [value.x, value.y, value.z, value.w]\n\telif value is Color:\n\t\treturn [value.r, value.g, value.b, value.a]\n\telif value is Resource and value.resource_path != \"\":\n\t\treturn value.resource_path\n\telse:\n\t\treturn value\n\nfunc extract_script_info(script_path: String) -> Dictionary:\n\tvar file = FileAccess.open(script_path, FileAccess.READ)\n\tif not file:\n\t\treturn {}\n\t\n\tvar content = file.get_as_text()\n\tfile.close()\n\t\n\tvar script_data = {\n\t\t\"path\": script_path,\n\t\t\"language\": script_path.get_extension(),\n\t\t\"content\": content,\n\t\t\"exports\": extract_exported_variables(content),\n\t\t\"signals\": extract_signals(content),\n\t\t\"functions\": extract_functions(content)\n\t}\n\t\n\treturn script_data\n\nfunc extract_exported_variables(content: String) -> Array:\n\tvar exports = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"@export\"):\n\t\t\texports.append(line)\n\t\n\treturn exports\n\nfunc extract_signals(content: String) -> Array:\n\tvar signals = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"signal \"):\n\t\t\tsignals.append(line)\n\t\n\treturn signals\n\nfunc extract_functions(content: String) -> Array:\n\tvar functions = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"func \"):\n\t\t\tfunctions.append(line)\n\t\n\treturn functions\n\nfunc get_autoloads() -> Dictionary:\n\tvar autoloads = {}\n\t\n\t# Get autoload settings from project settings\n\tvar settings = ProjectSettings.get_property_list()\n\t\n\tfor setting in settings:\n\t\tif setting.name.begins_with(\"autoload/\"):\n\t\t\tvar autoload_name = setting.name.substr(9)  # Remove \"autoload/\" prefix\n\t\t\tvar autoload_path = ProjectSettings.get_setting(setting.name)\n\t\t\tautoloads[autoload_name] = autoload_path\n\t\n\treturn autoloads\n\nfunc get_relevant_project_settings() -> Dictionary:\n\tvar settings = {\n\t\t\"application\": {},\n\t\t\"display\": {},\n\t\t\"input\": {},\n\t\t\"physics\": {},\n\t\t\"rendering\": {}\n\t}\n\t\n\t# Application settings\n\tsettings.application[\"name\"] = ProjectSettings.get_setting(\"application/config/name\", \"\")\n\tsettings.application[\"description\"] = ProjectSettings.get_setting(\"application/config/description\", \"\")\n\tsettings.application[\"main_scene\"] = ProjectSettings.get_setting(\"application/run/main_scene\", \"\")\n\t\n\t# Display settings\n\tsettings.display[\"width\"] = ProjectSettings.get_setting(\"display/window/size/viewport_width\", 0)\n\tsettings.display[\"height\"] = ProjectSettings.get_setting(\"display/window/size/viewport_height\", 0)\n\t\n\t# Input map\n\tvar input_map = {}\n\tfor action in InputMap.get_actions():\n\t\tinput_map[action] = []\n\t\tfor event in InputMap.action_get_events(action):\n\t\t\tinput_map[action].append(str(event))\n\tsettings.input[\"map\"] = input_map\n\t\n\treturn settings\n", "exports": [], "functions": ["func export_project_structure() -> bool:", "func find_files_recursive(path: String, extension: String) -> Array[String]:", "func parse_scene_structure(scene_path: String) -> Dictionary:", "func parse_node_recursive(node: Node) -> Dictionary:", "func get_non_default_properties(node: Node) -> Dictionary:", "func values_equal(a, b) -> bool:", "func serialize_value(value):", "func extract_script_info(script_path: String) -> Dictionary:", "func extract_exported_variables(content: String) -> Array:", "func extract_signals(content: String) -> Array:", "func extract_functions(content: String) -> Array:", "func get_autoloads() -> Dictionary:", "func get_relevant_project_settings() -> Dictionary:"], "language": "gd", "path": "res:///addons/ai_context_generator/ai_context_generator.gd", "signals": []}, {"content": "@tool\nextends EditorPlugin\n\nconst AIContextGenerator = preload(\"res://addons/ai_context_generator/ai_context_generator.gd\")\n\nfunc _enter_tree():\n\t# Add a simple menu item to the Project menu\n\tadd_tool_menu_item(\"Generate AI Context\", _on_generate_context)\n\nfunc _exit_tree():\n\t# Clean up\n\tremove_tool_menu_item(\"Generate AI Context\")\n\nfunc _on_generate_context():\n\tvar generator = AIContextGenerator.new()\n\tgenerator.export_project_structure()\n", "exports": [], "functions": ["func _enter_tree():", "func _exit_tree():", "func _on_generate_context():"], "language": "gd", "path": "res:///addons/ai_context_generator/plugin.gd", "signals": []}, {"content": "class_name AudioManager extends Node\n\n\n@export_category(\"Omni\")\n## List of audios. Similar to AudioStreamPlayer\n@export var audios_omni: Array[AudioMangerOmni] = []\n\n@export_category(\"2D\")\n## Parent node of the node tree.\n@export var target_parent_audios_2d: Node2D = null:\n\tset(value):\n\t\ttarget_parent_audios_2d = value\n\t\tif is_instance_valid(target_parent_audios_2d):\n\t\t\tfor audio in audios_2d:\n\t\t\t\tvar audio_controller: AudioManagerController2D = _get_audio_controller_2d(audio.audio_name)\n\t\t\t\tif is_instance_valid(audio_controller):\n\t\t\t\t\tif not audio_controller.is_inside_tree():\n\t\t\t\t\t\ttarget_parent_audios_2d.add_child(audio_controller)\n\t\t\t\t\telse:\n\t\t\t\t\t\taudio_controller.reparent(target_parent_audios_2d)\n\t\t\t\t\taudio_controller.position = Vector2.ZERO\n\t\telse:\n\t\t\tfor audio in audios_2d:\n\t\t\t\tvar audio_controller: AudioManagerController2D = _get_audio_controller_2d(audio.audio_name)\n\t\t\t\tif is_instance_valid(audio_controller):\n\t\t\t\t\tif not audio_controller.is_inside_tree():\n\t\t\t\t\t\ttarget_parent_audios_2d.add_child(audio_controller)\n\t\t\t\t\telse:\n\t\t\t\t\t\taudio_controller.reparent(get_parent())\n\t\t\t\t\taudio_controller.position = Vector2.ZERO\n\n## List of 2d audios. Similar to AudioStreamPlayer2D\n@export var audios_2d: Array[AudioManger2D] = []\n\n@export_category(\"3D\")\n## Parent node of the node tree.\n@export var target_parent_audios_3d: Node3D = null:\n\tset(value):\n\t\ttarget_parent_audios_3d = value\n\t\tif is_instance_valid(target_parent_audios_3d):\n\t\t\tfor audio in audios_3d:\n\t\t\t\tvar audio_controller: AudioManagerController3D = _get_audio_controller_3d(audio.audio_name)\n\t\t\t\tif is_instance_valid(audio_controller):\n\t\t\t\t\tif not audio_controller.is_inside_tree():\n\t\t\t\t\t\ttarget_parent_audios_3d.add_child(audio_controller)\n\t\t\t\t\telse:\n\t\t\t\t\t\taudio_controller.reparent(target_parent_audios_3d)\n\t\t\t\t\taudio_controller.position = Vector3.ZERO\n\t\telse:\n\t\t\tfor audio in audios_3d:\n\t\t\t\tvar audio_controller: AudioManagerController3D = _get_audio_controller_3d(audio.audio_name)\n\t\t\t\tif is_instance_valid(audio_controller):\n\t\t\t\t\tif not audio_controller.is_inside_tree():\n\t\t\t\t\t\ttarget_parent_audios_3d.add_child(audio_controller)\n\t\t\t\t\telse:\n\t\t\t\t\t\taudio_controller.reparent(get_parent())\n\t\t\t\t\taudio_controller.position = Vector3.ZERO\n\n## List of 3d audios. Similar to AudioStreamPlayer3D\n@export var audios_3d: Array[AudioManger3D] = []\n\n\nvar audios_manager_controller_omni: Dictionary = {}\nvar audios_manager_controller_2d: Dictionary = {}\nvar audios_manager_controller_3d: Dictionary = {}\n\nsignal finished_omni(audio_name: String)\nsignal finished_2d(audio_name: String)\nsignal finished_3d(audio_name: String)\n\nvar returns_values: int = 0\n\nfunc _ready() -> void:\n\t_init_audios_omni()\n\t_init_audios_2d()\n\t_init_audios_3d()\n\n\n## Play audio Omni by name\nfunc play_audio_omni(audio_name: String) -> void:\n\tvar audio: AudioManagerControllerOmni = _validate_audio_omni(audio_name)\n\tif not audio:\n\t\treturn\n\n\tif audio.is_randomizer:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif audio.is_interactive:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif audio.is_playlist:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif float(audio.duration) <= 0.0:\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_omni(audio_name)\n\n\tif audio.use_clipper:\n\t\taudio.call_deferred(\"play\", audio.start_time)\n\telse:\n\t\taudio.call_deferred(\"play\")\n\n\ttimer.call_deferred(\"start\")\n\n\n## Play audio 2D by name\nfunc play_audio_2d(audio_name: String) -> void:\n\tvar audio: AudioManagerController2D = _validate_audio_2d(audio_name)\n\tif not audio:\n\t\treturn\n\n\tif audio.is_randomizer:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif audio.is_interactive:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif audio.is_playlist:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif float(audio.duration) <= 0.0:\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_2d(audio_name)\n\n\tif audio.use_clipper:\n\t\taudio.call_deferred(\"play\", audio.start_time)\n\telse:\n\t\taudio.call_deferred(\"play\")\n\n\ttimer.call_deferred(\"start\")\n\n\n## Play audio 3D by name\nfunc play_audio_3d(audio_name: String) -> void:\n\tvar audio: AudioManagerController3D = _validate_audio_3d(audio_name)\n\tif not audio:\n\t\treturn\n\n\tif audio.is_randomizer:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif audio.is_interactive:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif audio.is_playlist:\n\t\taudio.call_deferred(\"play\")\n\t\treturn\n\n\tif float(audio.duration) <= 0.0:\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_3d(audio_name)\n\n\tif audio.use_clipper:\n\t\taudio.call_deferred(\"play\", audio.start_time)\n\telse:\n\t\taudio.call_deferred(\"play\")\n\n\ttimer.call_deferred(\"start\")\n\n\n## Pause audio Omni by name\nfunc pause_audio_omni(audio_name: String) -> void:\n\tvar audio: AudioManagerControllerOmni = _validate_audio_omni(audio_name)\n\tif not audio or audio.stream_paused:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.stream_paused = true\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_omni(audio_name)\n\taudio.stream_paused = true\n\taudio.time_remain = timer.time_left\n\ttimer.stop()\n\n\n## Pause audio 2D by name\nfunc pause_audio_2d(audio_name: String) -> void:\n\tvar audio: AudioManagerController2D = _validate_audio_2d(audio_name)\n\tif not audio or audio.stream_paused:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.stream_paused = true\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_2d(audio_name)\n\taudio.stream_paused = true\n\taudio.time_remain = timer.time_left\n\ttimer.stop()\n\n\n## Pause audio 3D by name\nfunc pause_audio_3d(audio_name: String) -> void:\n\tvar audio: AudioManagerController3D = _validate_audio_3d(audio_name)\n\tif not audio or audio.stream_paused:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.stream_paused = true\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_3d(audio_name)\n\taudio.stream_paused = true\n\taudio.time_remain = timer.time_left\n\ttimer.stop()\n\n\n## Continue audio Omni by name\nfunc continue_audio_omni(audio_name: String) -> void:\n\tvar audio: AudioManagerControllerOmni = _validate_audio_omni(audio_name)\n\tif not audio or not audio.stream_paused:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.stream_paused = false\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_omni(audio_name)\n\taudio.stream_paused = false\n\ttimer.start(audio.time_remain)\n\n\n## Continue audio 2D by name\nfunc continue_audio_2d(audio_name: String) -> void:\n\tvar audio: AudioManagerController2D = _validate_audio_2d(audio_name)\n\tif not audio or not audio.stream_paused:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.stream_paused = false\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_2d(audio_name)\n\taudio.stream_paused = false\n\ttimer.start(audio.time_remain)\n\n\n## Continue audio 3D by name\nfunc continue_audio_3d(audio_name: String) -> void:\n\tvar audio: AudioManagerController3D = _validate_audio_3d(audio_name)\n\tif not audio or not audio.stream_paused:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.stream_paused = false\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_3d(audio_name)\n\taudio.stream_paused = false\n\ttimer.start(audio.time_remain)\n\n\n## Stop audio Omni by name\nfunc stop_audio_omni(audio_name: String) -> void:\n\tvar audio: AudioManagerControllerOmni = _validate_audio_omni(audio_name)\n\tif not audio or not audio.playing:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.call_deferred(\"stop\")\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_omni(audio_name)\n\n\ttimer.stop()\n\taudio.stop()\n\n\n## Stop audio 2D by name\nfunc stop_audio_2d(audio_name: String) -> void:\n\tvar audio: AudioManagerController2D = _validate_audio_2d(audio_name)\n\tif not audio or not audio.playing:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.call_deferred(\"stop\")\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_2d(audio_name)\n\n\ttimer.stop()\n\taudio.stop()\n\n\n## Stop audio 3D by name\nfunc stop_audio_3d(audio_name: String) -> void:\n\tvar audio: AudioManagerController3D = _validate_audio_3d(audio_name)\n\tif not audio or not audio.playing:\n\t\treturn\n\n\tif not audio.is_inside_tree():\n\t\treturn\n\n\tif audio.is_randomizer or audio.is_interactive or audio.is_playlist:\n\t\taudio.call_deferred(\"stop\")\n\t\treturn\n\n\tvar timer: Timer = _setup_timer_3d(audio_name)\n\n\ttimer.stop()\n\taudio.stop()\n\n\n## Play all audios\nfunc play_all() -> void:\n\tfor a in audios_omni:\n\t\tplay_audio_omni(a.audio_name)\n\n\tfor a in audios_2d:\n\t\tplay_audio_2d(a.audio_name)\n\n\tfor a in audios_3d:\n\t\tplay_audio_3d(a.audio_name)\n\n\n## Play all audios omni\nfunc play_all_omni() -> void:\n\tfor a in audios_omni:\n\t\tplay_audio_omni(a.audio_name)\n\n\n## Play all audios 2D\nfunc play_all_2d() -> void:\n\tfor a in audios_2d:\n\t\tplay_audio_2d(a.audio_name)\n\n\n## Play all audios 3D\nfunc play_all_3d() -> void:\n\tfor a in audios_3d:\n\t\tplay_audio_3d(a.audio_name)\n\n\n## Stop all audios\nfunc stop_all() -> void:\n\tfor a in audios_omni:\n\t\tstop_audio_omni(a.audio_name)\n\n\tfor a in audios_2d:\n\t\tstop_audio_2d(a.audio_name)\n\n\tfor a in audios_3d:\n\t\tstop_audio_3d(a.audio_name)\n\n\n## Stop all audios Omni\nfunc stop_all_omni() -> void:\n\tfor a in audios_omni:\n\t\tstop_audio_omni(a.audio_name)\n\n\n## Stop all audios 2D\nfunc stop_all_2d() -> void:\n\tfor a in audios_2d:\n\t\tstop_audio_2d(a.audio_name)\n\n\n## Stop all audios\nfunc stop_all_3d() -> void:\n\tfor a in audios_3d:\n\t\tstop_audio_3d(a.audio_name)\n\n\n## Pause all audios\nfunc pause_all() -> void:\n\tfor a in audios_omni:\n\t\tpause_audio_omni(a.audio_name)\n\n\tfor a in audios_2d:\n\t\tpause_audio_2d(a.audio_name)\n\n\tfor a in audios_3d:\n\t\tpause_audio_3d(a.audio_name)\n\n\n## Pause all audios Omni\nfunc pause_all_omni() -> void:\n\tfor a in audios_omni:\n\t\tpause_audio_omni(a.audio_name)\n\n\n## Pause all audios\nfunc pause_all_2d() -> void:\n\tfor a in audios_2d:\n\t\tpause_audio_2d(a.audio_name)\n\n\n## Pause all audios\nfunc pause_all_3d() -> void:\n\tfor a in audios_3d:\n\t\tpause_audio_3d(a.audio_name)\n\n\n## Continue all audios\nfunc continue_all() -> void:\n\tfor a in audios_omni:\n\t\tcontinue_audio_omni(a.audio_name)\n\n\tfor a in audios_2d:\n\t\tcontinue_audio_2d(a.audio_name)\n\n\tfor a in audios_3d:\n\t\tcontinue_audio_3d(a.audio_name)\n\n\n## Continue all audios Omni\nfunc continue_all_omni() -> void:\n\tfor a in audios_omni:\n\t\tcontinue_audio_omni(a.audio_name)\n\n\n## Continue all audios\nfunc continue_all_2d() -> void:\n\tfor a in audios_2d:\n\t\tcontinue_audio_2d(a.audio_name)\n\n\n## Continue all audios\nfunc continue_all_3d() -> void:\n\tfor a in audios_3d:\n\t\tcontinue_audio_3d(a.audio_name)\n\n\nfunc is_playing_omni(audio_name: String) -> bool:\n\tvar audio: AudioMangerOmni = get_audio_omni(audio_name)\n\treturn audio._owner.playing if audio else false\n\n\nfunc is_playing_2d(audio_name: String) -> bool:\n\tvar audio: AudioManger2D = get_audio_2d(audio_name)\n\treturn audio._owner.playing if audio else false\n\n\nfunc is_playing_3d(audio_name: String) -> bool:\n\tvar audio: AudioManger3D = get_audio_3d(audio_name)\n\treturn audio._owner.playing if audio else false\n\n\n## Get audio 3D (AudioManger3D)\nfunc get_audio_3d(_audio_name: String) -> AudioManger3D:\n\tfor aud in audios_3d:\n\t\tif aud.audio_name == _audio_name:\n\t\t\treturn aud\n\tpush_warning(\"AudioManger3D %s not find.\"%_audio_name)\n\treturn null\n\n\n## Get audio Omni (AudioMangerOmni)\nfunc get_audio_omni(_audio_name: String) -> AudioMangerOmni:\n\tfor aud in audios_omni:\n\t\tif aud.audio_name == _audio_name:\n\t\t\treturn aud\n\tpush_warning(\"AudioMangerOmni %s not find.\"%_audio_name)\n\treturn null\n\n\n## Get audio 2D (AudioManger2D)\nfunc get_audio_2d(_audio_name: String) -> AudioManger2D:\n\tfor aud in audios_2d:\n\t\tif aud.audio_name == _audio_name:\n\t\t\treturn aud\n\tpush_warning(\"AudioManger2D %s not find.\"%_audio_name)\n\treturn null\n\n\n## Init audios instances\nfunc _init_audios_omni() -> void:\n\tfor audio_omni in audios_omni:\n\t\tif not _check_audio(audio_omni):\n\t\t\tcontinue\n\t\t_warning_audio(audio_omni)\n\n\t\tvar is_randomizer: bool = audio_omni.audio_stream is AudioStreamRandomizer\n\t\tvar is_interactive: bool = audio_omni.audio_stream is AudioStreamInteractive\n\t\tvar is_playlist: bool = audio_omni.audio_stream is AudioStreamPlaylist\n\n\t\tvar new_audio_manager_controller_omni: AudioManagerControllerOmni = AudioManagerControllerOmni.new(\n\t\t\taudio_omni.start_time, audio_omni.duration, audio_omni.use_clipper, audio_omni.loop, 0.0, false, is_randomizer, is_interactive, is_playlist\n\t\t\t)\n\n\t\taudio_omni._owner = new_audio_manager_controller_omni\n\t\t_setup_audio_properties_omni(new_audio_manager_controller_omni, audio_omni)\n\t\taudios_manager_controller_omni[audio_omni.audio_name] = new_audio_manager_controller_omni\n\t\tadd_child(new_audio_manager_controller_omni)\n\n\t\tif new_audio_manager_controller_omni.is_randomizer and audio_omni.auto_play:\n\t\t\tnew_audio_manager_controller_omni.call_deferred(\"play\")\n\t\t\treturn\n\n\t\tif new_audio_manager_controller_omni.is_interactive and audio_omni.auto_play:\n\t\t\tnew_audio_manager_controller_omni.call_deferred(\"play\")\n\t\t\treturn\n\n\t\tif audio_omni.duration > 0 and audio_omni.auto_play:\n\t\t\tplay_audio_omni.call_deferred(audio_omni.audio_name)\n\n\nfunc _init_audios_2d() -> void:\n\tfor audio_2d in audios_2d:\n\t\tif not _check_audio(audio_2d):\n\t\t\tcontinue\n\t\t_warning_audio(audio_2d)\n\n\t\tvar is_randomizer: bool = audio_2d.audio_stream is AudioStreamRandomizer\n\t\tvar is_interactive: bool = audio_2d.audio_stream is AudioStreamInteractive\n\t\tvar is_playlist: bool = audio_2d.audio_stream is AudioStreamPlaylist\n\n\t\tvar new_audio_manager_controller_2d: AudioManagerController2D = AudioManagerController2D.new(\n\t\t\taudio_2d.start_time, audio_2d.duration, audio_2d.use_clipper, audio_2d.loop, 0.0, false, is_randomizer, is_interactive, is_playlist\n\t\t\t)\n\n\t\taudio_2d._owner = new_audio_manager_controller_2d\n\t\t_setup_audio_properties_2d(new_audio_manager_controller_2d, audio_2d)\n\t\taudios_manager_controller_2d[audio_2d.audio_name] = new_audio_manager_controller_2d\n\n\t\tif is_instance_valid(target_parent_audios_2d):\n\t\t\ttarget_parent_audios_2d.add_child.call_deferred(new_audio_manager_controller_2d)\n\t\telse:\n\t\t\tif get_parent():\n\t\t\t\tget_parent().call_deferred(\"add_child\", new_audio_manager_controller_2d)\n\t\t\telse:\n\t\t\t\tadd_child(new_audio_manager_controller_2d)\n\n\t\tif new_audio_manager_controller_2d.is_randomizer and audio_2d.auto_play:\n\t\t\tnew_audio_manager_controller_2d.call_deferred(\"play\")\n\t\t\treturn\n\n\t\tif new_audio_manager_controller_2d.is_interactive and audio_2d.auto_play:\n\t\t\tnew_audio_manager_controller_2d.call_deferred(\"play\")\n\t\t\treturn\n\n\t\tif audio_2d.duration > 0 and audio_2d.auto_play:\n\t\t\tcall_deferred(\"play_audio_2d\", audio_2d.audio_name)\n\n\nfunc _init_audios_3d() -> void:\n\tfor audio_3d in audios_3d:\n\t\tif not _check_audio(audio_3d):\n\t\t\tcontinue\n\t\t_warning_audio(audio_3d)\n\n\t\tvar is_randomizer: bool = audio_3d.audio_stream is AudioStreamRandomizer\n\t\tvar is_interactive: bool = audio_3d.audio_stream is AudioStreamInteractive\n\t\tvar is_playlist: bool = audio_3d.audio_stream is AudioStreamPlaylist\n\n\t\tvar new_audio_manager_controller_3d: AudioManagerController3D = AudioManagerController3D.new(\n\t\t\taudio_3d.start_time, audio_3d.duration, audio_3d.use_clipper, audio_3d.loop, 0.0, false, is_randomizer, is_interactive, is_playlist\n\t\t\t)\n\n\t\taudio_3d._owner = new_audio_manager_controller_3d\n\n\t\t_setup_audio_properties_3d(new_audio_manager_controller_3d, audio_3d)\n\t\taudios_manager_controller_3d[audio_3d.audio_name] = new_audio_manager_controller_3d\n\n\t\tif is_instance_valid(target_parent_audios_3d):\n\t\t\ttarget_parent_audios_3d.add_child.call_deferred(new_audio_manager_controller_3d)\n\t\telse:\n\t\t\tif get_parent():\n\t\t\t\tget_parent().add_child.call_deferred(new_audio_manager_controller_3d)\n\t\t\telse:\n\t\t\t\tadd_child(new_audio_manager_controller_3d)\n\n\t\tif new_audio_manager_controller_3d.is_randomizer and audio_3d.auto_play:\n\t\t\tnew_audio_manager_controller_3d.call_deferred(\"play\")\n\t\t\treturn\n\n\t\tif new_audio_manager_controller_3d.is_interactive and audio_3d.auto_play:\n\t\t\tnew_audio_manager_controller_3d.call_deferred(\"play\")\n\t\t\treturn\n\n\n\t\tif audio_3d.duration > 0 and audio_3d.auto_play:\n\t\t\tplay_audio_3d.call_deferred(audio_3d.audio_name)\n\n\nfunc _setup_audio_properties_omni(audio: AudioStreamPlayer, a: AudioMangerOmni) -> void:\n\taudio.stream = a.audio_stream\n\taudio.volume_db = a.volume_db\n\taudio.pitch_scale = a.pitch_scale\n\taudio.mix_target = a.mix_target\n\taudio.max_polyphony = a.max_polyphony\n\taudio.bus = a.bus\n\taudio.playback_type = a.playback_type\n\n\nfunc _setup_audio_properties_2d(audio: AudioStreamPlayer2D, a: AudioManger2D) -> void:\n\taudio.stream = a.audio_stream\n\taudio.volume_db = a.volume_db\n\taudio.pitch_scale = a.pitch_scale\n\taudio.max_distance = a.max_distance\n\taudio.max_polyphony = a.max_polyphony\n\taudio.panning_strength = a.panning_strength\n\taudio.bus = a.bus\n\taudio.area_mask = a.area_mask\n\taudio.playback_type = a.playback_type\n\taudio.attenuation = a.attenuation\n\n\nfunc _setup_audio_properties_3d(audio: AudioStreamPlayer3D, a: AudioManger3D) -> void:\n\taudio.stream = a.audio_stream\n\taudio.volume_db = a.volume_db\n\taudio.max_db = a.max_db\n\taudio.pitch_scale = a.pitch_scale\n\taudio.max_distance = a.max_distance\n\taudio.unit_size = a.unit_size\n\taudio.max_polyphony = a.max_polyphony\n\taudio.panning_strength = a.panning_strength\n\taudio.bus = a.bus\n\taudio.area_mask = a.area_mask\n\taudio.playback_type = a.playback_type\n\taudio.emission_angle_degrees = a.emission_angle_degrees\n\taudio.emission_angle_enabled = a.emission_angle_enabled\n\taudio.emission_angle_filter_attenuation_db = a.emission_angle_filter_attenuation_db\n\taudio.attenuation_filter_cutoff_hz = a.attenuation_filter_cutoff_hz\n\taudio.attenuation_filter_db = a.attenuation_filter_db\n\taudio.doppler_tracking = a.doppler_tracking\n\n\nfunc _validate_audio_3d(_audio_name: String) -> AudioManagerController3D:\n\tvar audio: AudioManagerController3D = _get_audio_controller_3d(_audio_name)\n\tif not audio:\n\t\tpush_warning(\"AudioManger3D name (%s) not found.\" % _audio_name)\n\treturn audio\n\n\nfunc _validate_audio_omni(_audio_name: String) -> AudioManagerControllerOmni:\n\tvar audio: AudioManagerControllerOmni = _get_audio_controller_omni(_audio_name)\n\tif not audio:\n\t\tpush_warning(\"AudioMangerOmni name (%s) not found.\" % _audio_name)\n\treturn audio\n\n\nfunc _validate_audio_2d(_audio_name: String) -> AudioManagerController2D:\n\tvar audio: AudioManagerController2D = _get_audio_controller_2d(_audio_name)\n\tif not audio:\n\t\tpush_warning(\"AudioManger2D name (%s) not found.\" % _audio_name)\n\treturn audio\n\n\nfunc _setup_timer_omni(_audio_name: String) -> Timer:\n\tvar audio: AudioManagerControllerOmni = _get_audio_controller_omni(_audio_name) as AudioManagerControllerOmni\n\taudio.timer.one_shot = not audio.loop\n\taudio.timer.wait_time = max(audio.duration, 0.00001)\n\tif not audio.is_timer_connected:\n\t\treturns_values = audio.timer.timeout.connect(Callable(_on_timer_timeout_omni).bind(audio, _audio_name, func() -> void: play_audio_omni(_audio_name)))\n\t\taudio.is_timer_connected = true\n\treturn audio.timer\n\n\nfunc _setup_timer_2d(_audio_name: String) -> Timer:\n\tvar audio: AudioManagerController2D = _get_audio_controller_2d(_audio_name) as AudioManagerController2D\n\taudio.timer.one_shot = not audio.loop\n\taudio.timer.wait_time = max(audio.duration, 0.00001)\n\tif not audio.is_timer_connected:\n\t\treturns_values = audio.timer.timeout.connect(Callable(_on_timer_timeout_2d).bind(audio, _audio_name, func() -> void: play_audio_2d(_audio_name)))\n\t\taudio.is_timer_connected = true\n\treturn audio.timer\n\n\nfunc _setup_timer_3d(_audio_name: String) -> Timer:\n\tvar audio: AudioManagerController3D = _get_audio_controller_3d(_audio_name) as AudioManagerController3D\n\taudio.timer.one_shot = not audio.loop\n\taudio.timer.wait_time = max(audio.duration, 0.00001)\n\tif not audio.is_timer_connected:\n\t\treturns_values = audio.timer.timeout.connect(Callable(_on_timer_timeout_3d).bind(audio, _audio_name, func() -> void: play_audio_3d(_audio_name)))\n\t\taudio.is_timer_connected = true\n\treturn audio.timer\n\n\nfunc _on_timer_timeout_omni(_audio: AudioManagerControllerOmni, _audio_name: String, cb: Callable) -> void:\n\tif _audio.loop:\n\t\tcb.call()\n\telse:\n\t\tif not _audio.is_playlist:\n\t\t\t_audio.stop()\n\t\t\treturns_values = emit_signal(\"finished_omni\", _audio_name)\n\n\nfunc _on_timer_timeout_2d(_audio: AudioManagerController2D, _audio_name: String, cb: Callable) -> void:\n\tif _audio.loop:\n\t\tcb.call()\n\telse:\n\t\tif not _audio.is_playlist:\n\t\t\t_audio.stop()\n\t\t\treturns_values = emit_signal(\"finished_2d\", _audio_name)\n\n\nfunc _on_timer_timeout_3d(_audio: AudioManagerController3D, _audio_name: String, cb: Callable) -> void:\n\tif _audio.loop:\n\t\tcb.call()\n\telse:\n\t\tif not _audio.is_playlist:\n\t\t\t_audio.stop()\n\t\t\treturns_values = emit_signal(\"finished_3d\", _audio_name)\n\n\nfunc _get_audio_controller_omni(_audio_name: String) -> AudioManagerControllerOmni:\n\treturn audios_manager_controller_omni.get(_audio_name, null) as AudioManagerControllerOmni\n\n\nfunc _get_audio_controller_2d(_audio_name: String) -> AudioManagerController2D:\n\treturn audios_manager_controller_2d.get(_audio_name, null) as AudioManagerController2D\n\n\nfunc _get_audio_controller_3d(_audio_name: String) -> AudioManagerController3D:\n\treturn audios_manager_controller_3d.get(_audio_name, null) as AudioManagerController3D\n\n\nfunc _warning_audio(_audio: Variant) -> void:\n\t_audio._define_duration()\n\tif not _audio.audio_stream:\n\t\tpush_warning(\"The STREAM property cannot be null. (%s)\" % _audio.audio_name)\n\tif _audio.duration <= 0.0 and not (_audio.is_randomizer or _audio.is_interactive or _audio.is_synchronized or _audio.is_playlist):\n\t\tpush_warning(\"AudioManger duration cannot be less than or equal to zero. Check START_TIME, END_TIME. (%s)\" % _audio.audio_name)\n\tif _audio.use_clipper and not (_audio.is_randomizer or _audio.is_interactive or _audio.is_synchronized or _audio.is_playlist) and _audio.start_time > _audio.end_time:\n\t\tpush_warning(\"Start time cannot be greater than end time in AudioManger resource: (%s)\" % _audio.audio_name)\n\n\nfunc _check_audio(_audio: Variant) -> bool:\n\tif not _audio or not _audio.audio_stream:\n\t\tpush_warning(\"AudioManger resource or its stream is not properly defined.\")\n\t\treturn false\n\tif not (_audio.is_randomizer or _audio.is_interactive or _audio.is_synchronized or _audio.is_playlist) and _audio.start_time > _audio.end_time:\n\t\tpush_warning(\"AudioManger start time cannot be greater than end time for '%s'. AudioMangerResource deleted from ManagerList.\" % _audio.audio_name)\n\t\treturn false\n\treturn true\n\n\n#*****************************************************************************\nclass AudioManagerControllerOmni extends AudioStreamPlayer:\n\tvar timer: Timer\n\tvar start_time: float\n\tvar duration: float\n\tvar use_clipper: bool\n\tvar loop: bool:\n\t\tset(value):\n\t\t\tloop = value\n\t\t\tif (!loop and !timer.is_stopped()):\n\t\t\t\ttimer.stop()\n\tvar time_remain: float\n\tvar is_timer_connected: bool\n\tvar is_randomizer: bool\n\tvar is_interactive: bool\n\tvar is_playlist: bool\n\n\n\tfunc _init(_start_time: float, _duration: float, _use_clipper: bool, _loop: bool, _time_remain: float, _is_timer_connected: bool, _is_randomizer: bool = false, _is_interactive: bool = false, _is_playlist: bool = false) -> void:\n\t\ttimer = Timer.new()\n\t\ttimer.name = \"timer\"\n\t\tadd_child(timer)\n\t\tself.start_time = _start_time\n\t\tself.duration = _duration\n\t\tself.use_clipper = _use_clipper\n\t\tself.loop = _loop\n\t\tself.time_remain = _time_remain\n\t\tself.is_timer_connected = _is_timer_connected\n\t\tis_randomizer = _is_randomizer\n\t\tis_interactive = _is_interactive\n\t\tis_playlist = _is_playlist\n\n\n#*****************************************************************************\nclass AudioManagerController2D extends AudioStreamPlayer2D:\n\tvar timer: Timer\n\tvar start_time: float\n\tvar duration: float\n\tvar use_clipper: bool\n\tvar loop: bool:\n\t\tset(value):\n\t\t\tloop = value\n\t\t\tif (!loop and !timer.is_stopped()):\n\t\t\t\ttimer.stop()\n\tvar time_remain: float\n\tvar is_timer_connected: bool\n\tvar is_randomizer: bool\n\tvar is_interactive: bool\n\tvar is_playlist: bool\n\n\n\tfunc _init(_start_time: float, _duration: float, _use_clipper: bool, _loop: bool, _time_remain: float, _is_timer_connected: bool, _is_randomizer: bool = false, _is_interactive: bool = false, _is_playlist: bool = false) -> void:\n\t\ttimer = Timer.new()\n\t\ttimer.name = \"timer\"\n\t\tadd_child(timer)\n\t\tself.start_time = _start_time\n\t\tself.duration = _duration\n\t\tself.use_clipper = _use_clipper\n\t\tself.loop = _loop\n\t\tself.time_remain = _time_remain\n\t\tself.is_timer_connected = _is_timer_connected\n\t\tself.is_randomizer = _is_randomizer\n\t\tis_interactive = _is_interactive\n\t\tis_playlist = _is_playlist\n\n\n#*******************************************************************\nclass AudioManagerController3D extends AudioStreamPlayer3D:\n\tvar timer: Timer\n\tvar start_time: float\n\tvar duration: float\n\tvar use_clipper: bool\n\tvar loop: bool:\n\t\tset(value):\n\t\t\tloop = value\n\t\t\tif (!loop and !timer.is_stopped()):\n\t\t\t\ttimer.stop()\n\tvar time_remain: float\n\tvar is_timer_connected: bool\n\tvar is_randomizer: bool\n\tvar is_interactive: bool\n\tvar is_playlist: bool\n\n\n\tfunc _init(_start_time: float, _duration: float, _use_clipper: bool, _loop: bool, _time_remain: float, _is_timer_connected: bool, _is_randomizer: bool = false, _is_interactive: bool = false, _is_playlist: bool = false) -> void:\n\t\ttimer = Timer.new()\n\t\ttimer.name = \"timer\"\n\t\tadd_child(timer)\n\t\tself.start_time = _start_time\n\t\tself.duration = _duration\n\t\tself.use_clipper = _use_clipper\n\t\tself.loop = _loop\n\t\tself.time_remain = _time_remain\n\t\tself.is_timer_connected = _is_timer_connected\n\t\tis_randomizer = _is_randomizer\n\t\tis_interactive = _is_interactive\n\t\tis_playlist = _is_playlist\n", "exports": ["@export_category(\"Omni\")", "@export var audios_omni: Array[AudioMangerOmni] = []", "@export_category(\"2D\")", "@export var target_parent_audios_2d: Node2D = null:", "@export var audios_2d: Array[AudioManger2D] = []", "@export_category(\"3D\")", "@export var target_parent_audios_3d: Node3D = null:", "@export var audios_3d: Array[AudioManger3D] = []"], "functions": ["func _ready() -> void:", "func play_audio_omni(audio_name: String) -> void:", "func play_audio_2d(audio_name: String) -> void:", "func play_audio_3d(audio_name: String) -> void:", "func pause_audio_omni(audio_name: String) -> void:", "func pause_audio_2d(audio_name: String) -> void:", "func pause_audio_3d(audio_name: String) -> void:", "func continue_audio_omni(audio_name: String) -> void:", "func continue_audio_2d(audio_name: String) -> void:", "func continue_audio_3d(audio_name: String) -> void:", "func stop_audio_omni(audio_name: String) -> void:", "func stop_audio_2d(audio_name: String) -> void:", "func stop_audio_3d(audio_name: String) -> void:", "func play_all() -> void:", "func play_all_omni() -> void:", "func play_all_2d() -> void:", "func play_all_3d() -> void:", "func stop_all() -> void:", "func stop_all_omni() -> void:", "func stop_all_2d() -> void:", "func stop_all_3d() -> void:", "func pause_all() -> void:", "func pause_all_omni() -> void:", "func pause_all_2d() -> void:", "func pause_all_3d() -> void:", "func continue_all() -> void:", "func continue_all_omni() -> void:", "func continue_all_2d() -> void:", "func continue_all_3d() -> void:", "func is_playing_omni(audio_name: String) -> bool:", "func is_playing_2d(audio_name: String) -> bool:", "func is_playing_3d(audio_name: String) -> bool:", "func get_audio_3d(_audio_name: String) -> AudioManger3D:", "func get_audio_omni(_audio_name: String) -> AudioMangerOmni:", "func get_audio_2d(_audio_name: String) -> AudioManger2D:", "func _init_audios_omni() -> void:", "func _init_audios_2d() -> void:", "func _init_audios_3d() -> void:", "func _setup_audio_properties_omni(audio: AudioStreamPlayer, a: AudioMangerOmni) -> void:", "func _setup_audio_properties_2d(audio: AudioStreamPlayer2D, a: AudioManger2D) -> void:", "func _setup_audio_properties_3d(audio: AudioStreamPlayer3D, a: AudioManger3D) -> void:", "func _validate_audio_3d(_audio_name: String) -> AudioManagerController3D:", "func _validate_audio_omni(_audio_name: String) -> AudioManagerControllerOmni:", "func _validate_audio_2d(_audio_name: String) -> AudioManagerController2D:", "func _setup_timer_omni(_audio_name: String) -> Timer:", "func _setup_timer_2d(_audio_name: String) -> Timer:", "func _setup_timer_3d(_audio_name: String) -> Timer:", "func _on_timer_timeout_omni(_audio: AudioManagerControllerOmni, _audio_name: String, cb: Callable) -> void:", "func _on_timer_timeout_2d(_audio: AudioManagerController2D, _audio_name: String, cb: Callable) -> void:", "func _on_timer_timeout_3d(_audio: AudioManagerController3D, _audio_name: String, cb: Callable) -> void:", "func _get_audio_controller_omni(_audio_name: String) -> AudioManagerControllerOmni:", "func _get_audio_controller_2d(_audio_name: String) -> AudioManagerController2D:", "func _get_audio_controller_3d(_audio_name: String) -> AudioManagerController3D:", "func _warning_audio(_audio: Variant) -> void:", "func _check_audio(_audio: Variant) -> bool:", "func _init(_start_time: float, _duration: float, _use_clipper: bool, _loop: bool, _time_remain: float, _is_timer_connected: bool, _is_randomizer: bool = false, _is_interactive: bool = false, _is_playlist: bool = false) -> void:", "func _init(_start_time: float, _duration: float, _use_clipper: bool, _loop: bool, _time_remain: float, _is_timer_connected: bool, _is_randomizer: bool = false, _is_interactive: bool = false, _is_playlist: bool = false) -> void:", "func _init(_start_time: float, _duration: float, _use_clipper: bool, _loop: bool, _time_remain: float, _is_timer_connected: bool, _is_randomizer: bool = false, _is_interactive: bool = false, _is_playlist: bool = false) -> void:"], "language": "gd", "path": "res:///addons/audio_manager/audio_manager.gd", "signals": ["signal finished_omni(audio_name: String)", "signal finished_2d(audio_name: String)", "signal finished_3d(audio_name: String)"]}, {"content": "@tool\n\n\nclass_name AudioManger2D extends Resource\n\nvar _warning_duration: int = 0\nvar _can_warning_duration: bool = false\n\nvar _warning_randomizer_count: int = 0\n\nvar _waring_starttime_endtime: int = 0\nvar _can_warning_starttime_endtime: bool = false\n\nvar _owner: Variant = null\nvar _previous_duration: float = 0.0\n\n## Audio duration\nvar duration: float = 0.0:\n\tset(value):\n\t\tduration = value\n\nvar is_randomizer: bool = false\nvar is_interactive: bool = false\nvar is_synchronized: bool = false\nvar is_playlist: bool = false\n\n## Name of the audio to be called in the code\n@export var audio_name: String = \"\":\n\tset(value):\n\t\taudio_name = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(audio_name, \"NAME\")\n\n\n## Audio file\n@export var audio_stream: AudioStream = null:\n\tset(value):\n\t\taudio_stream = value\n\t\tis_randomizer = value is AudioStreamRandomizer\n\t\tis_interactive = value is AudioStreamInteractive\n\t\tis_synchronized = value is AudioStreamSynchronized\n\t\tis_playlist = value is AudioStreamPlaylist\n\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\tif loop: loop = false\n\t\t\tif use_clipper: use_clipper = false\n\t\t\tif start_time != 0.0: start_time = 0.0\n\t\t\tif end_time != 0.0: end_time = 0.0\n\t\t\tif loop_offset != 0.0: loop_offset = 0.0\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(audio_stream, \"STREAM\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.stream = value\n\t\t\t_owner.duration = duration\n\n\n## Enable or disable clipper in audio.\n## if true, you have to configure the start_time and and_time and the subtraction of end_time by start_time together with the loop_offset cannot be less than zero.\n@export var use_clipper: bool = false:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tuse_clipper = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(use_clipper, \"USE_CLIPPER\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.use_clipper = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Start time of audio in seconds when use_clipper is true.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var start_time: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tstart_time = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(start_time, \"START_TIME\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.start_time = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## End time of audio in seconds when use_clipper is true.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var end_time: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tend_time = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(end_time, \"END_TIME\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Set Volume Db\n@export_range(-80.0, 80.0, 0.01, \"suffix:db\") var volume_db: float = 0.0:\n\tset(value):\n\t\tvolume_db = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(volume_db, \"VOLUME_DB\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.volume_db = value\n\n\n## Set Pitch Scale\n@export_range(0.1, 4.0, 0.001) var pitch_scale: float = 1.0:\n\tset(value):\n\t\tpitch_scale = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(pitch_scale, \"PITCH_SCALE\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.pitch_scale = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Set Max Distance\n@export_range(0.1, 4096.0, 1.0, \"or_greater\", \"suffix:m\") var max_distance: float = 2000.0:\n\tset(value):\n\t\tmax_distance = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(max_distance, \"MAX_DISTANCE\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.max_distance = value\n\n\n## Set Loop\n@export var loop: bool = false:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot loop.\")\n\t\t\treturn\n\t\tloop = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(loop, \"LOOP\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.loop = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Audio rewinds in seconds when looping.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 1.0, 0.0001, \"or_greater\", \"suffix:sec\") var loop_offset: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of the Randomizer, Playlist or Interactive type cannot have a loop and therefore does not have a loopoffset.\")\n\t\t\treturn\n\t\tloop_offset = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(loop_offset, \"LOOP_OFFSET\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Play the audio as soon as you enter the scene.\n@export var auto_play: bool = false:\n\tset(value):\n\t\tauto_play = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(auto_play, \"AUTO_PLAY\")\n\n\n## Set Max Polyphony\n@export_range(1, 100, 1, \"or_greater\") var max_polyphony: int = 1:\n\tset(value):\n\t\tmax_polyphony = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(max_polyphony, \"MAX_POLYPHONY\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.max_polyphony = value\n\n\n## Set Panning Strength\n@export_range(0.0, 3.0, 0.01) var panning_strength: float = 1.0:\n\tset(value):\n\t\tpanning_strength = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(panning_strength, \"PANNING_STRENGTH\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.panning_strength = value\n\n\n@export_flags_2d_physics var area_mask: int = 1:\n\tset(value):\n\t\tarea_mask = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.area_mask = value\n\n\n@export var bus: StringName = \"Master\":\n\tset(value):\n\t\tbus = value\n\t\tif AudioServer.get_bus_index(value) == -1:\n\t\t\tpush_warning(\"Audio bus '%s' not found in AudioServer.\" % value)\n\t\t\treturn\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.bus = value\n\n\n@export var playback_type: AudioServer.PlaybackType = AudioServer.PLAYBACK_TYPE_DEFAULT:\n\tset(value):\n\t\tplayback_type = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.playback_type = value\n\n\n@export_exp_easing(\"attenuation\") var attenuation: float = 1.0:\n\tset(value):\n\t\tattenuation = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.attenuation = value\n\n\nfunc _increment_loop_offset() -> float:\n\tif loop:\n\t\treturn loop_offset\n\telse:\n\t\treturn 0.0\n\n\nfunc _define_duration() -> void:\n\t_previous_duration = duration\n\tif use_clipper:\n\t\tif audio_stream:\n\t\t\tduration = min(max(((end_time - start_time) - _increment_loop_offset()) / pitch_scale, 0.0), audio_stream.get_length())\n\t\telse:\n\t\t\tduration = 0.0\n\telse:\n\t\tif not is_instance_valid(audio_stream):\n\t\t\tduration = 0.0\n\t\telse:\n\t\t\tduration = max((audio_stream.get_length() - _increment_loop_offset()) / pitch_scale, 0.0)\n\t_warning_duration_zero()\n\n\nfunc _warning_start_time_with_end_time() -> void:\n\tif _waring_starttime_endtime >= 7:\n\t\tif not _can_warning_starttime_endtime:\n\t\t\t_can_warning_starttime_endtime = true\n\telse:\n\t\t_waring_starttime_endtime += 1\n\tif _can_warning_starttime_endtime and Engine.is_editor_hint() and audio_stream and use_clipper and start_time > end_time:\n\t\tpush_warning(\"Start time cannot be greater than end time in Audio resource: %s\" % audio_name)\n\n\nfunc _warning_property_null(value: Variant, property_string: String) -> void:\n\tif value is String:\n\t\tif value == \"\":\n\t\t\tpush_warning(\"The %s parameter cannot be null or empty. (%s)\" % [property_string, audio_name])\n\telse:\n\t\tif value == null:\n\t\t\tpush_warning(\"The %s parameter cannot be null or empty. (%s)\" % [property_string, audio_name])\n\n\nfunc _warning_duration_zero() -> void:\n\tif _warning_duration >= 7:\n\t\tif not _can_warning_duration:\n\t\t\t_can_warning_duration = true\n\telse:\n\t\t_warning_duration += 1\n\n\tif _can_warning_duration and Engine.is_editor_hint() and audio_stream and duration <= 0:\n\t\tpush_warning(\"The audio duration cannot be less than or equal to zero. Check the properties: START_TIME, END_TIME and LOOP_OFFSET.\")\n\n\nfunc _warning_randomizer(value: String) -> void:\n\tif _warning_randomizer_count > 13:\n\t\tpush_warning(value)\n\telse:\n\t\t_warning_randomizer_count += 1\n\n\nfunc get_audio_stream_player2d() -> AudioStreamPlayer2D:\n\treturn _owner as AudioStreamPlayer2D\n\n\nfunc _redefine_timeout() -> void:\n\tif not _owner.timer.is_stopped():\n\t\tvar elapsed_time: float = _previous_duration - _owner.timer.time_left\n\t\tvar progress: float = elapsed_time / _previous_duration if _previous_duration > 0 else 0.0\n\t\tvar new_remaining_time: float = duration * (1.0 - progress)\n\t\t_owner.timer.stop()\n\t\tvar cb_timeout: Callable\n\t\tfor connection: Variant in _owner.timer.get_signal_connection_list(\"timeout\"):\n\t\t\tcb_timeout = connection.callable\n\t\t\t_owner.timer.disconnect(\"timeout\", connection.callable)\n\t\t_owner.timer.wait_time = max(new_remaining_time, 0.0001)\n\t\t_owner.timer.timeout.connect(cb_timeout)\n\t\t_owner.timer.start()\n", "exports": ["@export var audio_name: String = \"\":", "@export var audio_stream: AudioStream = null:", "@export var use_clipper: bool = false:", "@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var start_time: float = 0.0:", "@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var end_time: float = 0.0:", "@export_range(-80.0, 80.0, 0.01, \"suffix:db\") var volume_db: float = 0.0:", "@export_range(0.1, 4.0, 0.001) var pitch_scale: float = 1.0:", "@export_range(0.1, 4096.0, 1.0, \"or_greater\", \"suffix:m\") var max_distance: float = 2000.0:", "@export var loop: bool = false:", "@export_range(0.0, 1.0, 0.0001, \"or_greater\", \"suffix:sec\") var loop_offset: float = 0.0:", "@export var auto_play: bool = false:", "@export_range(1, 100, 1, \"or_greater\") var max_polyphony: int = 1:", "@export_range(0.0, 3.0, 0.01) var panning_strength: float = 1.0:", "@export_flags_2d_physics var area_mask: int = 1:", "@export var bus: StringName = \"Master\":", "@export var playback_type: AudioServer.PlaybackType = AudioServer.PLAYBACK_TYPE_DEFAULT:", "@export_exp_easing(\"attenuation\") var attenuation: float = 1.0:"], "functions": ["func _increment_loop_offset() -> float:", "func _define_duration() -> void:", "func _warning_start_time_with_end_time() -> void:", "func _warning_property_null(value: Variant, property_string: String) -> void:", "func _warning_duration_zero() -> void:", "func _warning_randomizer(value: String) -> void:", "func get_audio_stream_player2d() -> AudioStreamPlayer2D:", "func _redefine_timeout() -> void:"], "language": "gd", "path": "res:///addons/audio_manager/audio_manager_2d.gd", "signals": []}, {"content": "@tool\n\n\nclass_name AudioManger3D extends Resource\n\nvar _warning_duration: int = 0\nvar _can_warning_duration: bool = false\n\nvar _warning_randomizer_count: int = 0\n\nvar _waring_starttime_endtime: int = 0\nvar _can_warning_starttime_endtime: bool = false\n\nvar _owner: Variant = null\nvar _previous_duration: float = 0.0\n\n## Audio duration\nvar duration: float = 0.0:\n\tset(value):\n\t\tduration = value\n\nvar is_randomizer: bool = false\nvar is_interactive: bool = false\nvar is_synchronized: bool = false\nvar is_playlist: bool = false\n\n## Name of the audio to be called in the code\n@export var audio_name: String = \"\":\n\tset(value):\n\t\taudio_name = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(audio_name, \"NAME\")\n\n\n## Audio file\n@export var audio_stream: AudioStream = null:\n\tset(value):\n\t\taudio_stream = value\n\t\tis_randomizer = value is AudioStreamRandomizer\n\t\tis_interactive = value is AudioStreamInteractive\n\t\tis_synchronized = value is AudioStreamSynchronized\n\t\tis_playlist = value is AudioStreamPlaylist\n\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\tif loop: loop = false\n\t\t\tif use_clipper: use_clipper = false\n\t\t\tif start_time != 0.0: start_time = 0.0\n\t\t\tif end_time != 0.0: end_time = 0.0\n\t\t\tif loop_offset != 0.0: loop_offset = 0.0\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(audio_stream, \"STREAM\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.stream = value\n\t\t\t_owner.duration = duration\n\n\n## Enable or disable clipper in audio.\n## if true, you have to configure the start_time and and_time and the subtraction of end_time by start_time together with the loop_offset cannot be less than zero.\n@export var use_clipper: bool = false:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tuse_clipper = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(use_clipper, \"USE_CLIPPER\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.use_clipper = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Start time of audio in seconds when use_clipper is true.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var start_time: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tstart_time = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(start_time, \"START_TIME\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.start_time = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## End time of audio in seconds when use_clipper is true.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var end_time: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tend_time = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(end_time, \"END_TIME\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Set Volume Db\n@export_range(-80.0, 80.0, 0.01, \"suffix:db\") var volume_db: float = 0.0:\n\tset(value):\n\t\tvolume_db = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(volume_db, \"VOLUME_DB\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.volume_db = value\n\n\n## Set Max Db\n@export_range(-24.0, 6.0, 0.01, \"suffix:db\") var max_db: float = 3.0:\n\tset(value):\n\t\tmax_db = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(max_db, \"MAX_DB\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.max_db = value\n\n\n## Set Pitch Scale\n@export_range(0.1, 4.0, 0.001) var pitch_scale: float = 1.0:\n\tset(value):\n\t\tpitch_scale = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(pitch_scale, \"PITCH_SCALE\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.pitch_scale = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Set Max Distance\n@export_range(0.1, 4096.0, 1.0, \"or_greater\", \"suffix:m\") var max_distance: float = 2000.0:\n\tset(value):\n\t\tmax_distance = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(max_distance, \"MAX_DISTANCE\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.max_distance = value\n\n\n## Set Unit Size\n@export_range(0.1, 100.0, 0.1, \"or_greater\") var unit_size: float = 10.0:\n\tset(value):\n\t\tunit_size = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(unit_size, \"UNIT_SIZE\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.unit_size = value\n\n\n## Set Loop\n@export var loop: bool = false:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot loop.\")\n\t\t\treturn\n\t\tloop = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(loop, \"LOOP\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.loop = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Audio rewinds in seconds when looping.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 1.0, 0.0001, \"or_greater\", \"suffix:sec\") var loop_offset: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of the Randomizer, Playlist or Interactive type cannot have a loop and therefore does not have a loopoffset.\")\n\t\t\treturn\n\t\tloop_offset = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(loop_offset, \"LOOP_OFFSET\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Play the audio as soon as you enter the scene.\n@export var auto_play: bool = false:\n\tset(value):\n\t\tauto_play = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(auto_play, \"AUTO_PLAY\")\n\n\n## Set Max Polyphony\n@export_range(1, 100, 1, \"or_greater\") var max_polyphony: int = 1:\n\tset(value):\n\t\tmax_polyphony = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(max_polyphony, \"MAX_POLYPHONY\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.max_polyphony = value\n\n\n## Set Panning Strength\n@export_range(0.0, 3.0, 0.01) var panning_strength: float = 1.0:\n\tset(value):\n\t\tpanning_strength = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(panning_strength, \"PANNING_STRENGTH\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.panning_strength = value\n\n\n@export_flags_2d_physics var area_mask: int = 1:\n\tset(value):\n\t\tarea_mask = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.area_mask = value\n\n\n@export var bus: StringName = \"Master\":\n\tset(value):\n\t\tbus = value\n\t\tif AudioServer.get_bus_index(value) == -1:\n\t\t\tpush_warning(\"Audio bus '%s' not found in AudioServer.\" % value)\n\t\t\treturn\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.bus = value\n\n\n@export var playback_type: AudioServer.PlaybackType = AudioServer.PLAYBACK_TYPE_DEFAULT:\n\tset(value):\n\t\tplayback_type = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.playback_type = value\n\n\n@export var emission_angle_enabled: bool = false:\n\tset(value):\n\t\temission_angle_enabled = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.emission_angle_enabled = value\n\n\n@export_range(0.1, 90.0, 0.001, \"suffix:deg\") var emission_angle_degrees: float = 45.0:\n\tset(value):\n\t\temission_angle_degrees = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.emission_angle_degrees = value\n\n\n@export_range(-80.0, 0.0, 0.001, \"suffix:db\") var emission_angle_filter_attenuation_db: float = -12.0:\n\tset(value):\n\t\temission_angle_filter_attenuation_db = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.emission_angle_filter_attenuation_db = value\n\n\n@export_range(1, 20500, 1, \"suffix:hz\") var attenuation_filter_cutoff_hz: int = 5000:\n\tset(value):\n\t\tattenuation_filter_cutoff_hz = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.attenuation_filter_cutoff_hz = value\n\n\n@export_range(-80.0, 0.0, 0.001, \"suffix:db\") var attenuation_filter_db: float = -24.0:\n\tset(value):\n\t\tattenuation_filter_db = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.attenuation_filter_db = value\n\n\n@export var doppler_tracking: AudioStreamPlayer3D.DopplerTracking = AudioStreamPlayer3D.DopplerTracking.DOPPLER_TRACKING_DISABLED:\n\tset(value):\n\t\tdoppler_tracking = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.doppler_tracking = value\n\n\nfunc _increment_loop_offset() -> float:\n\tif loop:\n\t\treturn loop_offset\n\telse:\n\t\treturn 0.0\n\n\nfunc _define_duration() -> void:\n\t_previous_duration = duration\n\tif use_clipper:\n\t\tif audio_stream:\n\t\t\tduration = min(max(((end_time - start_time) - _increment_loop_offset()) / pitch_scale, 0.0), audio_stream.get_length())\n\t\telse:\n\t\t\tduration = 0.0\n\telse:\n\t\tif not is_instance_valid(audio_stream):\n\t\t\tduration = 0.0\n\t\telse:\n\t\t\tduration = max((audio_stream.get_length() - _increment_loop_offset()) / pitch_scale, 0.0)\n\t_warning_duration_zero()\n\n\nfunc _warning_start_time_with_end_time() -> void:\n\tif _waring_starttime_endtime >= 7:\n\t\tif not _can_warning_starttime_endtime:\n\t\t\t_can_warning_starttime_endtime = true\n\telse:\n\t\t_waring_starttime_endtime += 1\n\tif _can_warning_starttime_endtime and Engine.is_editor_hint() and audio_stream and use_clipper and start_time > end_time:\n\t\tpush_warning(\"Start time cannot be greater than end time in Audio resource: %s\" % audio_name)\n\n\nfunc _warning_property_null(value: Variant, property_string: String) -> void:\n\tif value is String:\n\t\tif value == \"\":\n\t\t\tpush_warning(\"The %s parameter cannot be null or empty. (%s)\" % [property_string, audio_name])\n\telse:\n\t\tif value == null:\n\t\t\tpush_warning(\"The %s parameter cannot be null or empty. (%s)\" % [property_string, audio_name])\n\n\nfunc _warning_duration_zero() -> void:\n\tif _warning_duration >= 7:\n\t\tif not _can_warning_duration:\n\t\t\t_can_warning_duration = true\n\telse:\n\t\t_warning_duration += 1\n\n\tif not is_randomizer and _can_warning_duration and Engine.is_editor_hint() and audio_stream and duration <= 0:\n\t\tpush_warning(\"The audio duration cannot be less than or equal to zero. Check the properties: START_TIME, END_TIME and LOOP_OFFSET.\")\n\n\nfunc _warning_randomizer(value: String) -> void:\n\tif _warning_randomizer_count > 5:\n\t\tpush_warning(value)\n\telse:\n\t\t_warning_randomizer_count += 1\n\n\nfunc get_audio_stream_player3d() -> AudioStreamPlayer3D:\n\treturn _owner as AudioStreamPlayer3D\n\n\nfunc _redefine_timeout() -> void:\n\tif not _owner.timer.is_stopped():\n\t\tvar elapsed_time: float = _previous_duration - _owner.timer.time_left\n\t\tvar progress: float = elapsed_time / _previous_duration if _previous_duration > 0 else 0.0\n\t\tvar new_remaining_time: float = duration * (1.0 - progress)\n\t\t_owner.timer.stop()\n\t\tvar cb_timeout: Callable\n\t\tfor connection: Variant in _owner.timer.get_signal_connection_list(\"timeout\"):\n\t\t\tcb_timeout = connection.callable\n\t\t\t_owner.timer.disconnect(\"timeout\", connection.callable)\n\t\t_owner.timer.wait_time = max(new_remaining_time, 0.0001)\n\t\t_owner.timer.timeout.connect(cb_timeout)\n\t\t_owner.timer.start()\n", "exports": ["@export var audio_name: String = \"\":", "@export var audio_stream: AudioStream = null:", "@export var use_clipper: bool = false:", "@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var start_time: float = 0.0:", "@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var end_time: float = 0.0:", "@export_range(-80.0, 80.0, 0.01, \"suffix:db\") var volume_db: float = 0.0:", "@export_range(-24.0, 6.0, 0.01, \"suffix:db\") var max_db: float = 3.0:", "@export_range(0.1, 4.0, 0.001) var pitch_scale: float = 1.0:", "@export_range(0.1, 4096.0, 1.0, \"or_greater\", \"suffix:m\") var max_distance: float = 2000.0:", "@export_range(0.1, 100.0, 0.1, \"or_greater\") var unit_size: float = 10.0:", "@export var loop: bool = false:", "@export_range(0.0, 1.0, 0.0001, \"or_greater\", \"suffix:sec\") var loop_offset: float = 0.0:", "@export var auto_play: bool = false:", "@export_range(1, 100, 1, \"or_greater\") var max_polyphony: int = 1:", "@export_range(0.0, 3.0, 0.01) var panning_strength: float = 1.0:", "@export_flags_2d_physics var area_mask: int = 1:", "@export var bus: StringName = \"Master\":", "@export var playback_type: AudioServer.PlaybackType = AudioServer.PLAYBACK_TYPE_DEFAULT:", "@export var emission_angle_enabled: bool = false:", "@export_range(0.1, 90.0, 0.001, \"suffix:deg\") var emission_angle_degrees: float = 45.0:", "@export_range(-80.0, 0.0, 0.001, \"suffix:db\") var emission_angle_filter_attenuation_db: float = -12.0:", "@export_range(1, 20500, 1, \"suffix:hz\") var attenuation_filter_cutoff_hz: int = 5000:", "@export_range(-80.0, 0.0, 0.001, \"suffix:db\") var attenuation_filter_db: float = -24.0:", "@export var doppler_tracking: AudioStreamPlayer3D.DopplerTracking = AudioStreamPlayer3D.DopplerTracking.DOPPLER_TRACKING_DISABLED:"], "functions": ["func _increment_loop_offset() -> float:", "func _define_duration() -> void:", "func _warning_start_time_with_end_time() -> void:", "func _warning_property_null(value: Variant, property_string: String) -> void:", "func _warning_duration_zero() -> void:", "func _warning_randomizer(value: String) -> void:", "func get_audio_stream_player3d() -> AudioStreamPlayer3D:", "func _redefine_timeout() -> void:"], "language": "gd", "path": "res:///addons/audio_manager/audio_manager_3d.gd", "signals": []}, {"content": "@tool\n\n\nclass_name AudioMangerOmni extends Resource\n\nvar _warning_duration: int = 0\nvar _can_warning_duration: bool = false\n\nvar _warning_randomizer_count: int = 0\n\nvar _waring_starttime_endtime: int = 0\nvar _can_warning_starttime_endtime: bool = false\n\nvar _owner: Variant = null\nvar _previous_duration: float = 0.0\n\n## Audio duration\nvar duration: float = 0.0:\n\tset(value):\n\t\tduration = value\n\nvar is_randomizer: bool = false\nvar is_interactive: bool = false\nvar is_synchronized: bool = false\nvar is_playlist: bool = false\n\n## Name of the audio to be called in the code\n@export var audio_name: String = \"\":\n\tset(value):\n\t\taudio_name = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(audio_name, \"NAME\")\n\n\n## Audio file\n@export var audio_stream: AudioStream = null:\n\tset(value):\n\t\taudio_stream = value\n\t\tis_randomizer = value is AudioStreamRandomizer\n\t\tis_interactive = value is AudioStreamInteractive\n\t\tis_synchronized = value is AudioStreamSynchronized\n\t\tis_playlist = value is AudioStreamPlaylist\n\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\tif loop: loop = false\n\t\t\tif use_clipper: use_clipper = false\n\t\t\tif start_time != 0.0: start_time = 0.0\n\t\t\tif end_time != 0.0: end_time = 0.0\n\t\t\tif loop_offset != 0.0: loop_offset = 0.0\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(audio_stream, \"STREAM\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.stream = value\n\t\t\t_owner.duration = duration\n\n\n## Enable or disable clipper in audio.\n## if true, you have to configure the start_time and and_time and the subtraction of end_time by start_time together with the loop_offset cannot be less than zero.\n@export var use_clipper: bool = false:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tuse_clipper = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(use_clipper, \"USE_CLIPPER\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.use_clipper = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Start time of audio in seconds when use_clipper is true.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var start_time: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tstart_time = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(start_time, \"START_TIME\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.start_time = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## End time of audio in seconds when use_clipper is true.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var end_time: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot be trimmed.\")\n\t\t\treturn\n\t\tend_time = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(end_time, \"END_TIME\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Set Volume Db\n@export_range(-80.0, 80.0, 0.01, \"suffix:db\") var volume_db: float = 0.0:\n\tset(value):\n\t\tvolume_db = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(volume_db, \"VOLUME_DB\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.volume_db = value\n\n\n## Set Pitch Scale\n@export_range(0.1, 4.0, 0.001) var pitch_scale: float = 1.0:\n\tset(value):\n\t\tpitch_scale = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(pitch_scale, \"PITCH_SCALE\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.pitch_scale = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Set Unit Size\n@export var mix_target: AudioStreamPlayer.MixTarget = AudioStreamPlayer.MixTarget.MIX_TARGET_STEREO:\n\tset(value):\n\t\tmix_target = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(mix_target, \"MIX_TARGET\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.mix_target = value\n\n\n## Set Loop\n@export var loop: bool = false:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of type Randomizer, Playlist or Interactive cannot loop.\")\n\t\t\treturn\n\t\tloop = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(loop, \"LOOP\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.loop = value\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Audio rewinds in seconds when looping.\n## Remember: the value of end_time minus the value of start_time minus the value of loop_offset cannot be less than zero.\n@export_range(0.0, 1.0, 0.0001, \"or_greater\", \"suffix:sec\") var loop_offset: float = 0.0:\n\tset(value):\n\t\tif is_randomizer or is_interactive or is_playlist:\n\t\t\t_warning_randomizer(\"AudioStream of the Randomizer, Playlist or Interactive type cannot have a loop and therefore does not have a loopoffset.\")\n\t\t\treturn\n\t\tloop_offset = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(loop_offset, \"LOOP_OFFSET\")\n\t\t_define_duration()\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.duration = duration\n\t\t\t_redefine_timeout()\n\n\n## Play the audio as soon as you enter the scene.\n@export var auto_play: bool = false:\n\tset(value):\n\t\tauto_play = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(auto_play, \"AUTO_PLAY\")\n\n\n## Set Max Polyphony\n@export_range(1, 100, 1, \"or_greater\") var max_polyphony: int = 1:\n\tset(value):\n\t\tmax_polyphony = value\n\t\t_warning_start_time_with_end_time()\n\t\t_warning_property_null(max_polyphony, \"MAX_POLYPHONY\")\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.max_polyphony = value\n\n\n@export var bus: StringName = \"Master\":\n\tset(value):\n\t\tbus = value\n\t\tif AudioServer.get_bus_index(value) == -1:\n\t\t\tpush_warning(\"Audio bus '%s' not found in AudioServer.\" % value)\n\t\t\treturn\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.bus = value\n\n\n@export var playback_type: AudioServer.PlaybackType = AudioServer.PLAYBACK_TYPE_DEFAULT:\n\tset(value):\n\t\tplayback_type = value\n\t\tif is_instance_valid(_owner):\n\t\t\t_owner.playback_type = value\n\n\nfunc _increment_loop_offset() -> float:\n\tif loop:\n\t\treturn loop_offset\n\telse:\n\t\treturn 0.0\n\n\nfunc _define_duration() -> void:\n\t_previous_duration = duration\n\tif use_clipper:\n\t\tif audio_stream:\n\t\t\tduration = min(max(((end_time - start_time) - _increment_loop_offset()) / pitch_scale, 0.0), audio_stream.get_length())\n\t\telse:\n\t\t\tduration = 0.0\n\telse:\n\t\tif not is_instance_valid(audio_stream):\n\t\t\tduration = 0.0\n\t\telse:\n\t\t\tduration = max((audio_stream.get_length() - _increment_loop_offset()) / pitch_scale, 0.0)\n\t_warning_duration_zero()\n\n\nfunc _warning_start_time_with_end_time() -> void:\n\tif _waring_starttime_endtime >= 7:\n\t\tif not _can_warning_starttime_endtime:\n\t\t\t_can_warning_starttime_endtime = true\n\telse:\n\t\t_waring_starttime_endtime += 1\n\tif _can_warning_starttime_endtime and Engine.is_editor_hint() and audio_stream and use_clipper and start_time > end_time:\n\t\tpush_warning(\"Start time cannot be greater than end time in Audio resource: %s\" % audio_name)\n\n\nfunc _warning_property_null(value: Variant, property_string: String) -> void:\n\tif value is String:\n\t\tif value == \"\":\n\t\t\tpush_warning(\"The %s parameter cannot be null or empty. (%s)\" % [property_string, audio_name])\n\telse:\n\t\tif value == null:\n\t\t\tpush_warning(\"The %s parameter cannot be null or empty. (%s)\" % [property_string, audio_name])\n\n\nfunc _warning_duration_zero() -> void:\n\tif _warning_duration >= 7:\n\t\tif not _can_warning_duration:\n\t\t\t_can_warning_duration = true\n\telse:\n\t\t_warning_duration += 1\n\n\tif _can_warning_duration and Engine.is_editor_hint() and audio_stream and duration <= 0:\n\t\tpush_warning(\"The audio duration cannot be less than or equal to zero. Check the properties: START_TIME, END_TIME and LOOP_OFFSET.\")\n\n\nfunc _warning_randomizer(value: String) -> void:\n\tif _warning_randomizer_count > 13:\n\t\tpush_warning(value)\n\telse:\n\t\t_warning_randomizer_count += 1\n\n\nfunc get_audio_stream_player() -> AudioStreamPlayer:\n\t\treturn _owner as AudioStreamPlayer\n\n\nfunc _redefine_timeout() -> void:\n\tif not _owner.timer.is_stopped():\n\t\tvar elapsed_time: float = _previous_duration - _owner.timer.time_left\n\t\tvar progress: float = elapsed_time / _previous_duration if _previous_duration > 0 else 0.0\n\t\tvar new_remaining_time: float = duration * (1.0 - progress)\n\t\t_owner.timer.stop()\n\t\tvar cb_timeout: Callable\n\t\tfor connection: Variant in _owner.timer.get_signal_connection_list(\"timeout\"):\n\t\t\tcb_timeout = connection.callable\n\t\t\t_owner.timer.disconnect(\"timeout\", connection.callable)\n\t\t_owner.timer.wait_time = max(new_remaining_time, 0.0001)\n\t\t_owner.timer.timeout.connect(cb_timeout)\n\t\t_owner.timer.start()\n", "exports": ["@export var audio_name: String = \"\":", "@export var audio_stream: AudioStream = null:", "@export var use_clipper: bool = false:", "@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var start_time: float = 0.0:", "@export_range(0.0, 300.0, 0.01, \"or_greater\", \"suffix:sec\") var end_time: float = 0.0:", "@export_range(-80.0, 80.0, 0.01, \"suffix:db\") var volume_db: float = 0.0:", "@export_range(0.1, 4.0, 0.001) var pitch_scale: float = 1.0:", "@export var mix_target: AudioStreamPlayer.MixTarget = AudioStreamPlayer.MixTarget.MIX_TARGET_STEREO:", "@export var loop: bool = false:", "@export_range(0.0, 1.0, 0.0001, \"or_greater\", \"suffix:sec\") var loop_offset: float = 0.0:", "@export var auto_play: bool = false:", "@export_range(1, 100, 1, \"or_greater\") var max_polyphony: int = 1:", "@export var bus: StringName = \"Master\":", "@export var playback_type: AudioServer.PlaybackType = AudioServer.PLAYBACK_TYPE_DEFAULT:"], "functions": ["func _increment_loop_offset() -> float:", "func _define_duration() -> void:", "func _warning_start_time_with_end_time() -> void:", "func _warning_property_null(value: Variant, property_string: String) -> void:", "func _warning_duration_zero() -> void:", "func _warning_randomizer(value: String) -> void:", "func get_audio_stream_player() -> AudioStreamPlayer:", "func _redefine_timeout() -> void:"], "language": "gd", "path": "res:///addons/audio_manager/audio_manager_omni.gd", "signals": []}, {"content": "@tool\nextends EditorPlugin\n\nvar icon_omni: CompressedTexture2D = preload(\"res://addons/audio_manager/images/icon-omni-16x16.png\")\nvar icon_2d: CompressedTexture2D = preload(\"res://addons/audio_manager/images/icon-2d-16x16.png\")\nvar icon_3d: CompressedTexture2D = preload(\"res://addons/audio_manager/images/icon-3d-16x16.png\")\n\nvar main_script: Script = preload(\"res://addons/audio_manager/audio_manager.gd\")\n\nvar audio_manager_omni: Resource = preload(\"res://addons/audio_manager/audio_manager_omni.gd\")\nvar audio_manager_2d: Resource = preload(\"res://addons/audio_manager/audio_manager_2d.gd\")\nvar audio_manager_3d: Resource = preload(\"res://addons/audio_manager/audio_manager_3d.gd\")\n\n\nfunc _enter_tree() -> void:\n\tadd_custom_type(\"AudioManager\", \"Node\", main_script, icon_omni)\n\tadd_custom_type(\"AudioMangerOmni\", \"Resource\", audio_manager_omni, icon_omni)\n\tadd_custom_type(\"AudioManger2D\", \"Resource\", audio_manager_2d, icon_2d)\n\tadd_custom_type(\"AudioManger3D\", \"Resource\", audio_manager_3d, icon_3d)\n\n\nfunc _exit_tree() -> void:\n\tremove_custom_type(\"AudioManager\")\n\tremove_custom_type(\"AudioMangerOmni\")\n\tremove_custom_type(\"AudioManger2D\")\n\tremove_custom_type(\"AudioManger3D\")\n", "exports": [], "functions": ["func _enter_tree() -> void:", "func _exit_tree() -> void:"], "language": "gd", "path": "res:///addons/audio_manager/plugin.gd", "signals": []}, {"content": "extends Line2D\n\n@export_category('Trail')\n@export var length : = 10\n\n@onready var parent : Node2D = get_parent()\nvar offset : = Vector2.ZERO\n\nfunc _ready() -> void:\n\ttop_level = true\n\nfunc _physics_process(_delta: float) -> void:\n\toffset = position\n\tglobal_position = Vector2.ZERO\n\n\tvar point : = parent.global_position + offset\n\tadd_point(point, 0)\n\t\n\tif get_point_count() > length:\n\t\tremove_point(get_point_count() - 1)\n", "exports": ["@export_category('Trail')", "@export var length : = 10"], "functions": ["func _ready() -> void:", "func _physics_process(_delta: float) -> void:"], "language": "gd", "path": "res:///addons/trail_2d/trail_2d.gd", "signals": []}, {"content": "@tool\nextends EditorPlugin\n\n\nfunc _enter_tree() -> void:\n\tadd_custom_type('Trail2D', 'Line2D', preload('res://addons/trail_2d/trail_2d.gd'), preload('res://addons/trail_2d/icon.svg'))\n\n\nfunc _exit_tree() -> void:\n\t# Clean-up of the plugin goes here.\n\tremove_custom_type('Trail2D')\n", "exports": [], "functions": ["func _enter_tree() -> void:", "func _exit_tree() -> void:"], "language": "gd", "path": "res:///addons/trail_2d/trail_2d_plugin.gd", "signals": []}, {"content": "extends Node2D\nconst COLLECTABLE_CRYSTAL = preload(\"res://Scenes/Collectables/Collectable_Crystal.tscn\")\nconst COLLECTABLE_ENERGY_CORE = preload(\"res://Scenes/Collectables/Collectable_EnergyCore.tscn\")\nconst COLLECTABLE_SATELLITE = preload(\"res://Scenes/Collectables/Collectable_Satellite.tscn\")\nconst COLLECTABLE_STAR = preload(\"res://Scenes/Collectables/Collectable_Star.tscn\")\n\n@export var OrbitRangeMax : float = 300\n@export var OrbitRangeMin : float = 1\n@export var OrbitSpeedMax : float = 1.5\n@export var OrbitSpeedMin : float = 0.6\n\n@onready var collision_shape_2d: CollisionShape2D = $\"../CollisionShape2D\"\n@onready var sprite_2d: Sprite2D = $\"../CollisionShape2D/Sprite2D\"\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tvar scenes : Array = [COLLECTABLE_CRYSTAL,COLLECTABLE_ENERGY_CORE,COLLECTABLE_SATELLITE,COLLECTABLE_STAR]\n\tif(randi()%5 == 4): #1/5 chance no collectable\n\t\treturn\n\telse:\n\t\tvar collectable : Collectable = scenes.pick_random().instantiate()\n\t\tcollectable.orbit_radius = randf_range(sprite_2d.texture.get_width()*0.5, collision_shape_2d.shape.get_rect().size.x*0.25)\n\t\tcollectable.find_orbit_planet(owner)\n\t\t#collectable.orbit_center = global_position\n\t\tcollectable.orbit_speed = randf_range(OrbitSpeedMin, OrbitSpeedMax)\n\t\tadd_child(collectable)\n\t\t\n\tpass # Replace with function body.\n\n\n# Called every frame. 'delta' is the elapsed time since the previous frame.\nfunc _process(delta: float) -> void:\n\tpass\n", "exports": ["@export var OrbitRangeMax : float = 300", "@export var OrbitRangeMin : float = 1", "@export var OrbitSpeedMax : float = 1.5", "@export var OrbitSpeedMin : float = 0.6"], "functions": ["func _ready() -> void:", "func _process(delta: float) -> void:"], "language": "gd", "path": "res:///Scenes/CollectableSpawner.gd", "signals": []}, {"content": "extends CPUParticles2D\nclass_name ParticleEffect\n\nfunc Emit(excludeChildren : bool = false):\n\temitting = true\n\tif(excludeChildren):\n\t\treturn\n\tfor child : CPUParticles2D in get_children().filter(func(a): return a is CPUParticles2D):\n\t\tchild.emitting = true\n\treturn\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tpass # Replace with function body.\n\n\n# Called every frame. 'delta' is the elapsed time since the previous frame.\nfunc _process(_delta: float) -> void:\n\tpass\n", "exports": [], "functions": ["func Emit(excludeChildren : bool = false):", "func _ready() -> void:", "func _process(_delta: float) -> void:"], "language": "gd", "path": "res:///Scenes/particle_effect.gd", "signals": []}, {"content": "extends ColorRect\n\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tpass # Replace with function body.\n\nfunc _process(_delta: float) -> void:\n\t#material.set(\"shader_parameter/paralax\", amount)\n\treturn\n\n\t\n", "exports": [], "functions": ["func _ready() -> void:", "func _process(_delta: float) -> void:"], "language": "gd", "path": "res:///Scenes/UI/StarBackground_Menu.gd", "signals": []}, {"content": "extends HBoxContainer\nclass_name VolumeControl\n@onready var label: Label = $Label\n@onready var slider: HSlider = $HSlider2\n\n@export var busName : String\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tif(not busName):\n\t\tprinterr(name + \" LabelSlider does not have a busName\")\n\t\treturn\n\tlabel.text = \"- \" + busName\n\tslider.value_changed.connect(func(a): \n\t\tvar wantedVolume = linear_to_db(a)\n\t\tAudioServer.set_bus_volume_db(AudioServer.get_bus_index(busName), wantedVolume))\n\tpass # Replace with function body.\n\n\n# Called every frame. 'delta' is the elapsed time since the previous frame.\nfunc _process(delta: float) -> void:\n\tpass\n", "exports": ["@export var busName : String"], "functions": ["func _ready() -> void:", "func _process(delta: float) -> void:"], "language": "gd", "path": "res:///Scenes/UI/volume_control.gd", "signals": []}, {"content": "#https://www.youtube.com/watch?v=LGt-jjVf-ZU\nextends Camera2D\nclass_name ScreenShake\n\n@export var randomStrength: float = 10.0\n@export var shakeFade: float = 10.0\n\nvar rng = RandomNumberGenerator.new()\n\nvar shake_str: float = 0.0\n\nfunc Shake():\n\tshake_str = randomStrength\n\nfunc _process(delta: float) -> void:\n\t\n\tif(shake_str >0):\n\t\tshake_str = lerpf(shake_str,0,shakeFade*delta)\n\t\toffset = RandomOffset()\n\nfunc RandomOffset() -> Vector2:\n\treturn Vector2(rng.randf_range(-shake_str, shake_str), rng.randf_range(-shake_str, shake_str))\n\nfunc _input(event: InputEvent) -> void:\n\t#Godot forums\n\tif event is InputEventMouseButton:\n\t\tif event.is_pressed():\n\t\t\t# zoom in\n\t\t\tif event.button_index == MOUSE_BUTTON_WHEEL_UP:\n\t\t\t\tzoom *= 1.1\n\t\t\t\t# call the zoom function\n\t\t\t# zoom out\n\t\t\tif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:\n\t\t\t\t# call the zoom function\n\t\t\t\tzoom *= 0.9\n\t\tzoom = zoom.clamp(Vector2(0.1,0.1), Vector2(1.5,1.5))\n\t\n\t\tpass\n\t\n", "exports": ["@export var randomStrength: float = 10.0", "@export var shakeFade: float = 10.0"], "functions": ["func Shake():", "func _process(delta: float) -> void:", "func RandomOffset() -> Vector2:", "func _input(event: InputEvent) -> void:"], "language": "gd", "path": "res:///Scripts/camera_shake.gd", "signals": []}, {"content": "extends Area2D\nclass_name Collectable\n\n# Collectable types enum\nenum CollectableType {\n\tSTAR,\n\tSATELLITE,\n\tCRYSTAL,\n\tENERGY_CORE,\n\tMETEOR_FRAGMENT\n}\n\n# Signals\nsignal collected(collectable: Collectable)\n\n# Export variables\n@export var collectable_type: CollectableType = CollectableType.STAR\n@export var point_value: int = 10\n@export var orbit_radius: float = 150.0\n@export var orbit_speed: float = 1.0\n@export var collection_name: String = \"Star\"\n\n# Value degradation system\n@export var value_degradation_interval: float = 1.0  # Time in seconds between value reductions\n@export var value_degradation_amount: int = 1  # Amount to reduce value by each interval\n@export var minimum_value: int = 1  # Minimum value the collectable can have\n\n# Internal variables\nvar orbit_center: Vector2\nvar orbit_angle: float = 0.0\nvar is_collected: bool = false\nvar planet_reference: Area2D\n\n# Value degradation variables\nvar original_point_value: int\nvar degradation_timer: float = 0.0\n\n# Node references\n@onready var sprite: Sprite2D = $Sprite2D\n@onready var collection_area: CollisionShape2D = $CollisionShape2D\n@onready var collection_particles: CPUParticles2D = $CollectionParticles\n\nfunc _ready() -> void:\n\t# Add to collectables group\n\tadd_to_group(\"collectables\")\n\n\t# Connect to body entered signal for collection detection\n\tbody_entered.connect(_on_body_entered)\n\n\t# Set up collection particles but don't emit yet\n\tif collection_particles:\n\t\tcollection_particles.emitting = false\n\t\tsetup_collection_particles()\n\n\t#orbit planet gets set externally now.\n\t# Find the nearest planet to orbit around\n\t#find_orbit_planet()\n\n\t# Store original point value for degradation system\n\toriginal_point_value = point_value\n\n\t# Set random starting angle\n\torbit_angle = randf() * 2 * PI\n\nfunc _physics_process(delta: float) -> void:\n\tif is_collected or not planet_reference:\n\t\treturn\n\n\t# Update value degradation timer\n\tdegradation_timer += delta\n\tif degradation_timer >= value_degradation_interval:\n\t\tdegradation_timer = 0.0\n\t\tdegrade_value()\n\n\t# Update orbit position\n\torbit_angle += orbit_speed * delta\n\tif orbit_angle > 2 * PI:\n\t\torbit_angle -= 2 * PI\n\n\t# Calculate new position based on orbit\n\tvar orbit_offset = Vector2(cos(orbit_angle), sin(orbit_angle)) * orbit_radius\n\tglobal_position = orbit_center + orbit_offset\n\n\t# Rotate the sprite for visual effect\n\tsprite.rotation += orbit_speed * delta * 0.5\n\nfunc find_orbit_planet(overridePlanet : BasePlanet = null) -> void:\n\tif(overridePlanet):\n\t\tplanet_reference = overridePlanet\n\t\torbit_center = overridePlanet.global_position\n\t\treturn\n\t# Find the closest planet to orbit around\n\tvar planets = get_tree().get_nodes_in_group(\"planets\")\n\tif planets.is_empty():\n\t\t# Fallback: find any BasePlanet in the scene\n\t\tplanets = find_all_planets(get_tree().current_scene)\n\n\tif planets.is_empty():\n\t\tprint(\"Warning: No planets found for collectable to orbit!\")\n\t\treturn\n\n\tvar closest_planet : BasePlanet = null\n\tvar closest_distance = INF\n\n\tfor planet in planets:\n\t\tvar distance = global_position.distance_to(planet.global_position)\n\t\tif distance < closest_distance:\n\t\t\tclosest_distance = distance\n\t\t\tclosest_planet = planet\n\n\tif closest_planet:\n\t\tplanet_reference = closest_planet\n\t\torbit_center = closest_planet.global_position\n\t\t# Removed for dynamic spawning.\n\t\t\n\t\t# Adjust orbit radius based on distance to planet\n\t\t#var distance_to_planet = global_position.distance_to(orbit_center)\n\t\t#if distance_to_planet > 50:  # If we're far from the planet, use that distance \n\t\t\t#orbit_radius = distance_to_planet\n\nfunc find_all_planets(node: Node) -> Array:\n\tvar planets = []\n\tif node is BasePlanet:\n\t\tplanets.append(node)\n\tfor child in node.get_children():\n\t\tplanets.append_array(find_all_planets(child))\n\treturn planets\n\nfunc _on_body_entered(body: Node2D) -> void:\n\tif is_collected:\n\t\treturn\n\t\t\n\tif body is Player:\n\t\tcollect()\n\nfunc collect() -> void:\n\tif is_collected:\n\t\treturn\n\t\t\n\tis_collected = true\n\t\n\t# Play collection particles\n\tif collection_particles:\n\t\tcollection_particles.emitting = true\n\t\n\t# Add score\n\tGameManager.add_score(point_value)\n\t\n\t# Emit collected signal\n\tcollected.emit(self)\n\t\n\t# Hide the sprite and collision\n\tsprite.visible = false\n\tcollection_area.set_deferred(\"disabled\", true)\n\t\n\t# Wait for particles to finish, then remove\n\tawait get_tree().create_timer(1.0).timeout\n\tqueue_free()\n\n#You can't dynamically load on a web build; you have to load at build time.\nconst STAR_PARTICLES = preload(\"res://Assets/kenney_simple-space/PNG/Default/star_small.png\")\n\nfunc setup_collection_particles() -> void:\n\tif not collection_particles:\n\t\treturn\n\t\t\n\t# Configure particles based on collectable type\n\tmatch collectable_type:\n\t\tCollectableType.STAR:\n\t\t\tcollection_particles.texture = STAR_PARTICLES\n\t\t\tcollection_particles.amount = 20\n\t\t\tcollection_particles.color = Color.YELLOW\n\t\tCollectableType.SATELLITE:\n\t\t\tcollection_particles.amount = 15\n\t\t\tcollection_particles.color = Color.CYAN\n\t\tCollectableType.CRYSTAL:\n\t\t\tcollection_particles.amount = 25\n\t\t\tcollection_particles.color = Color.MAGENTA\n\t\tCollectableType.ENERGY_CORE:\n\t\t\tcollection_particles.amount = 30\n\t\t\tcollection_particles.color = Color.GREEN\n\t\tCollectableType.METEOR_FRAGMENT:\n\t\t\tcollection_particles.amount = 10\n\t\t\tcollection_particles.color = Color.ORANGE\n\t\n\t# Common particle settings\n\tcollection_particles.lifetime = 0.5\n\tcollection_particles.explosiveness = 1.0\n\tcollection_particles.direction = Vector2(0, -1)\n\tcollection_particles.initial_velocity_min = 50.0\n\tcollection_particles.initial_velocity_max = 100.0\n\tcollection_particles.gravity = Vector2(0, 98)\n\tcollection_particles.scale_amount_min = 0.5\n\tcollection_particles.scale_amount_max = 1.5\n\nfunc degrade_value() -> void:\n\t# Reduce the point value over time, but don't go below minimum\n\tif point_value > minimum_value:\n\t\tpoint_value = max(point_value - value_degradation_amount, minimum_value)\n\nfunc get_collectable_info() -> Dictionary:\n\treturn {\n\t\t\"type\": collectable_type,\n\t\t\"name\": collection_name,\n\t\t\"points\": point_value\n\t}\n", "exports": ["@export var collectable_type: CollectableType = CollectableType.STAR", "@export var point_value: int = 10", "@export var orbit_radius: float = 150.0", "@export var orbit_speed: float = 1.0", "@export var collection_name: String = \"Star\"", "@export var value_degradation_interval: float = 1.0  # Time in seconds between value reductions", "@export var value_degradation_amount: int = 1  # Amount to reduce value by each interval", "@export var minimum_value: int = 1  # Minimum value the collectable can have"], "functions": ["func _ready() -> void:", "func _physics_process(delta: float) -> void:", "func find_orbit_planet(overridePlanet : BasePlanet = null) -> void:", "func find_all_planets(node: Node) -> Array:", "func _on_body_entered(body: Node2D) -> void:", "func collect() -> void:", "func setup_collection_particles() -> void:", "func degrade_value() -> void:", "func get_collectable_info() -> Dictionary:"], "language": "gd", "path": "res:///Scripts/Collectable.gd", "signals": ["signal collected(collectable: Collectable)"]}, {"content": "extends Control\nclass_name CollectionNotification\n\n# Node references\n@onready var notification_label: Label = $NotificationLabel\n@onready var fade_timer: Timer = $FadeTimer\n\n# Animation variables\nvar fade_duration: float = 2.0\nvar slide_distance: float = 50.0\nvar initial_position: Vector2\n\nfunc _ready() -> void:\n\t# Set up the timer\n\tfade_timer.wait_time = fade_duration\n\tfade_timer.one_shot = true\n\tfade_timer.timeout.connect(_on_fade_timer_timeout)\n\t\n\t# Store initial position\n\tinitial_position = position\n\t\n\t# Start invisible\n\tmodulate.a = 0.0\n\nfunc show_collection(collectable_name: String, points: int) -> void:\n\t# Set the text\n\tnotification_label.text = \"+%d %s\" % [points, collectable_name]\n\t\n\t# Reset position and make visible\n\tposition = initial_position\n\tmodulate.a = 1.0\n\t\n\t# Start the fade animation\n\tfade_timer.start()\n\t\n\t# Create slide up animation\n\tvar tween = create_tween()\n\ttween.parallel().tween_property(self, \"position\", initial_position + Vector2(0, -slide_distance), fade_duration)\n\ttween.parallel().tween_property(self, \"modulate:a\", 0.0, fade_duration)\n\nfunc _on_fade_timer_timeout() -> void:\n\t# Remove this notification\n\tqueue_free()\n\n# Static method to create and show a notification\nstatic func create_notification(parent: Node, collectable_name: String, points: int, screen_position: Vector2) -> CollectionNotification:\n\t# Load the notification scene\n\tvar notification_scene = preload(\"res://Scenes/UI/CollectionNotification.tscn\")\n\tvar localNotification = notification_scene.instantiate()\n\t\n\t# Add to parent\n\tparent.add_child(localNotification)\n\t\n\t# Position it\n\tlocalNotification.position = screen_position\n\t\n\t# Show the collection\n\tlocalNotification.show_collection(collectable_name, points)\n\t\n\treturn localNotification\n", "exports": [], "functions": ["func _ready() -> void:", "func show_collection(collectable_name: String, points: int) -> void:", "func _on_fade_timer_timeout() -> void:"], "language": "gd", "path": "res:///Scripts/CollectionNotification.gd", "signals": []}, {"content": "extends Control\n\n@onready var home_icon: TextureRect = %HomeIcon\n@onready var planet_icons_container: Control = %PlanetIcons\n\nvar player: RigidBody2D\nvar home_planet: Area2D\nvar planets: Array[Area2D] = []\nvar planet_icons: Array[TextureRect] = []\n\n# --- Minimap Settings ---\n# The radius of the map circle in pixels.\n@export var map_radius: float = 50.0\n# How much to \"zoom\" the map. Smaller numbers = more zoomed out.\n@export var map_scale: float = 0.01\n\n# Planet icon texture\nvar planet_texture: Texture2D = preload(\"res://Assets/kenney_simple-space/PNG/Default/meteor_small.png\")\nfunc _ready():\n\t\n\tvar background: TextureRect = $Background\n\tbackground.modulate = Color(0.1, 0.1, 0.2, 0.8)\n\nfunc setup_compass(player_ref: RigidBody2D, home_ref: Area2D, planets_ref: Array[Area2D]):\n\tplayer = player_ref\n\thome_planet = home_ref\n\tplanets = planets_ref\n\tcreate_planet_icons()\n\nfunc create_planet_icons():\n\t# Clear existing icons\n\tfor icon in planet_icons:\n\t\tif is_instance_valid(icon):\n\t\t\ticon.queue_free()\n\tplanet_icons.clear()\n\t\n\t# Create icons for each planet\n\tfor _i in range(planets.size()):\n\t\tvar planet_icon = TextureRect.new()\n\t\tplanet_icon.texture = planet_texture\n\t\tplanet_icon.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL\n\t\tplanet_icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED\n\t\tplanet_icon.size = Vector2(16, 16)\n\t\tplanet_icon.pivot_offset = planet_icon.size / 2\n\t\tplanet_icons_container.add_child(planet_icon)\n\t\tplanet_icons.append(planet_icon)\n\nfunc _process(_delta):\n\tif not is_instance_valid(player) or not is_instance_valid(home_planet):\n\t\treturn\n\t\n\tupdate_map()\n\nfunc update_map():\n\tvar player_pos = player.global_position\n\t\n\tupdate_icon_position(home_icon, home_planet.global_position, player_pos)\n\t\n\tfor i in range(min(planets.size(), planet_icons.size())):\n\t\tif is_instance_valid(planets[i]) and is_instance_valid(planet_icons[i]):\n\t\t\tupdate_icon_position(planet_icons[i], planets[i].global_position, player_pos)\n\nfunc update_icon_position(icon: TextureRect, world_pos: Vector2, player_pos: Vector2):\n\t# 1. Get the vector from the player to the object in the game world.\n\tvar relative_pos = world_pos - player_pos\n\t\n\t# 2. Scale that huge world vector down to our tiny map scale.\n\tvar map_pos = relative_pos * map_scale\n\t\n\t# 3. If the object is outside our map's radius, clamp it to the edge.\n\tif map_pos.length() > map_radius:\n\t\tmap_pos = map_pos.normalized() * map_radius\n\n\t# 4. Set the icon's position on the map.\n\ticon.position = map_pos + $CompassCenter.size / 2.0\n\t\n\t# --- Visuals (Color & Size) ---\n\tvar distance = player_pos.distance_to(world_pos)\n\tvar scale_factor = clamp(2000.0 / distance, 0.6, 1.5) # Adjust sizing\n\ticon.scale = Vector2(scale_factor, scale_factor)\n\n\tif icon == home_icon:\n\t\ticon.modulate = Color.CYAN\n\t\tvar pulse = sin(Time.get_ticks_msec() / 200.0) * 0.1 + 1.0\n\t\ticon.scale *= pulse\n\telse:\n\t\tif distance < 1500:\n\t\t\ticon.modulate = Color.GREEN\n\t\telif distance < 5000:\n\t\t\ticon.modulate = Color.YELLOW\n\t\telse:\n\t\t\ticon.modulate = Color.RED\n", "exports": ["@export var map_radius: float = 50.0", "@export var map_scale: float = 0.01"], "functions": ["func _ready():", "func setup_compass(player_ref: RigidBody2D, home_ref: Area2D, planets_ref: Array[Area2D]):", "func create_planet_icons():", "func _process(_delta):", "func update_map():", "func update_icon_position(icon: TextureRect, world_pos: Vector2, player_pos: Vector2):"], "language": "gd", "path": "res:///Scripts/Compass.gd", "signals": []}, {"content": "extends Line2D\n\nvar point\n\nfunc ready():\n\ttop_level = (true)\n\nfunc _physics_process(delta):\n\tpoint = get_parent().global_position\n\tadd_point(point)\n\n\tif points.size() > 150:\n\t\tremove_point(0)\n", "exports": [], "functions": ["func ready():", "func _physics_process(delta):"], "language": "gd", "path": "res:///Scripts/Effects/trial.gd", "signals": []}, {"content": "extends Node2D\nclass_name <PERSON><PERSON><PERSON><PERSON><PERSON>\n\n@onready var player: Player = $Player\n@onready var hud: GameHUD = $HUDLayer/GameHUD\n\n# Find the home planet and other planets dynamically\nvar home_planet: HomePlanet\nvar all_planets: Array[Area2D] = []\n\n# Collectable tracking for win condition\nvar total_collectables: int = 0\nvar collected_collectables: int = 0\nvar collectable_counts_by_type: Dictionary = {}\n\nfunc _ready() -> void:\n\t# Find all planets and the home planet in the scene\n\tfor child in get_children():\n\t\tif child is HomePlanet:\n\t\t\thome_planet = child\n\t\t# Check if the node is a standard planet\n\t\tif child.get_script() == load(\"res://Scripts/planet.gd\"):\n\t\t\tall_planets.append(child)\n\t\t\t\n\tif not is_instance_valid(home_planet):\n\t\tprint(\"ERROR: <PERSON><PERSON><PERSON>roll<PERSON> could not find the HomePlanet node!\")\n\t\treturn\n\t\t\n\tif not is_instance_valid(hud):\n\t\tprint(\"ERROR: GameController could not find the HUD node!\")\n\t\treturn\n\n\t# Set up HUD references with all the found nodes\n\thud.setup_references(player, home_planet, all_planets)\n\n\t# Connect to collectable collection signals\n\tcall_deferred(\"connect_collectables\")\n\nfunc connect_collectables() -> void:\n\t# Initialize tracking dictionaries\n\tcollectable_counts_by_type = {\n\t\t\"Star\": {\"collected\": 0, \"total\": 0},\n\t\t\"Satellite\": {\"collected\": 0, \"total\": 0},\n\t\t\"Crystal\": {\"collected\": 0, \"total\": 0},\n\t\t\"Energy Core\": {\"collected\": 0, \"total\": 0}\n\t}\n\n\t# Find and connect to all collectables\n\tvar collectables = get_tree().get_nodes_in_group(\"collectables\")\n\ttotal_collectables = 0\n\tfor collectable in collectables:\n\t\tif collectable is Collectable:\n\t\t\tcollectable.collected.connect(_on_collectable_collected)\n\t\t\ttotal_collectables += 1\n\n\t\t\t# Count by type\n\t\t\tvar type_name = collectable.collection_name\n\t\t\tif type_name in collectable_counts_by_type:\n\t\t\t\tcollectable_counts_by_type[type_name][\"total\"] += 1\n\n\tprint(\"Found \", total_collectables, \" collectables in the scene\")\n\tfor type_name in collectable_counts_by_type:\n\t\tprint(\"  \", type_name, \": \", collectable_counts_by_type[type_name][\"total\"])\n\nfunc _on_collectable_collected(collectable: Collectable) -> void:\n\t# Show notification in HUD\n\tvar info = collectable.get_collectable_info()\n\thud.show_collection_notification(info[\"name\"], info[\"points\"])\n\n\t# Update collection count\n\tcollected_collectables += 1\n\n\t# Update count by type\n\tvar type_name = collectable.collection_name\n\tif type_name in collectable_counts_by_type:\n\t\tcollectable_counts_by_type[type_name][\"collected\"] += 1\n\n\t# Update HUD with new counts\n\thud.update_collectable_counts()\n\n\t# Check win condition\n\tcheck_win_condition()\n\nfunc check_win_condition() -> void:\n\tif collected_collectables >= total_collectables and total_collectables > 0:\n\t\tprint(\"All collectables collected! You win!\")\n\t\tGameManager.show_win_screen()\n\nfunc get_collectable_counts() -> Dictionary:\n\t# Return the accurately tracked counts\n\treturn collectable_counts_by_type\n", "exports": [], "functions": ["func _ready() -> void:", "func connect_collectables() -> void:", "func _on_collectable_collected(collectable: Collectable) -> void:", "func check_win_condition() -> void:", "func get_collectable_counts() -> Dictionary:"], "language": "gd", "path": "res:///Scripts/GameController.gd", "signals": []}, {"content": "extends Control\nclass_name GameHUD\n\n# UI element references\n@onready var score_label: Label = $VBoxContainer/ScoreLabel\n@onready var boosts_label: Label = $VBoxContainer/BoostsLabel\n@onready var boost_power_label: Label = $VBoxContainer/BoostPowerLabel\n@onready var compass = %Compass\n@onready var objectives_panel: ObjectivesPanel = $ObjectivesPanel\n@onready var notification_container: Control = $NotificationContainer\n\n# References to game objects\nvar player: Player\nvar game_controller: GameController\n\nfunc _ready() -> void:\n\tGameManager.score_changed.connect(_on_score_changed)\n\t\n\tupdate_score_display()\n\tupdate_boosts_display()\n\tboost_power_label.visible = false\n\n\t# Set up notification container position (bottom right)\n\tif notification_container:\n\t\tnotification_container.position = Vector2(get_viewport().size.x - 250, get_viewport().size.y - 100)\n\nfunc setup_references(player_ref: RigidBody2D, home_ref: Area2D, planets_ref: Array[Area2D]) -> void:\n\tplayer = player_ref\n\n\t# Get reference to game controller\n\tgame_controller = get_node(\"../../\") as GameController\n\n\tif compass:\n\t\tcompass.setup_compass(player, home_ref, planets_ref)\n\nfunc _process(_delta: float) -> void:\n\tif not is_instance_valid(player):\n\t\treturn\n\t\n\tupdate_boost_power_display()\n\tupdate_boosts_display()\n\nfunc _on_score_changed(_new_score: int) -> void:\n\tupdate_score_display()\n\nfunc update_score_display() -> void:\n\tvar score_text = \"Score: \" + str(GameManager.get_score())\n\n\t# Add collectable counts if game controller is available\n\tif game_controller:\n\t\tvar collectable_parts = []\n\t\tvar counts = game_controller.get_collectable_counts()\n\t\tvar collectable_order = [\"Star\", \"Satellite\", \"Crystal\", \"Energy Core\"]\n\n\t\tfor type_name in collectable_order:\n\t\t\t# Only display collectables that actually exist in the level\n\t\t\tif type_name in counts and counts[type_name][\"total\"] > 0:\n\t\t\t\tvar collected = counts[type_name][\"collected\"]\n\t\t\t\tvar total = counts[type_name][\"total\"]\n\t\t\t\tcollectable_parts.append(\"%s %d/%d\" % [type_name, collected, total])\n\t\t\n\t\t# If there are any collectables to display, join them with commas and add to the score text\n\t\tif not collectable_parts.is_empty():\n\t\t\tscore_text += \"      \" + \"    \".join(collectable_parts)\n\n\tscore_label.text = score_text\n\nfunc update_boosts_display() -> void:\n\tif player:\n\t\tvar boost_text = \"Boosts: \" + str(player.BoostCount)#(\"1\" if player.has_boost else \"0 (USED)\")\n\t\tboosts_label.text = boost_text\n\nfunc update_boost_power_display() -> void:\n\tif player.current_state == Player.State.AIMING:\n\t\tboost_power_label.visible = true\n\t\tvar power_percentage = player._current_aim_pull_vector.length() / player.max_pull_distance\n\t\tvar power_int = int(clamp(power_percentage, 0.0, 1.0) * 100)\n\t\tboost_power_label.text = \"Launch Power: %d%%\" % power_int\n\telse:\n\t\tboost_power_label.visible = false\n\nfunc update_collectable_counts() -> void:\n\t# Update the score display which now includes collectable counts\n\tupdate_score_display()\n\nfunc show_collection_notification(collectable_name: String, points: int) -> void:\n\tif notification_container:\n\t\t# Create notification at bottom right\n\t\tvar screen_pos = Vector2(0, -40 * notification_container.get_child_count())\n\t\tCollectionNotification.create_notification(notification_container, collectable_name, points, screen_pos)\n", "exports": [], "functions": ["func _ready() -> void:", "func setup_references(player_ref: RigidBody2D, home_ref: Area2D, planets_ref: Array[Area2D]) -> void:", "func _process(_delta: float) -> void:", "func _on_score_changed(_new_score: int) -> void:", "func update_score_display() -> void:", "func update_boosts_display() -> void:", "func update_boost_power_display() -> void:", "func update_collectable_counts() -> void:", "func show_collection_notification(collectable_name: String, points: int) -> void:"], "language": "gd", "path": "res:///Scripts/GameHUD.gd", "signals": []}, {"content": "extends Node\n\n# Singleton for managing game state and settings\nsignal ship_color_changed(new_color: Color)\nsignal score_changed(new_score: int)\n\n# Ship color settings\nvar ship_color: Color = Color.WHITE\nvar ship_color_hue: float = 0.0  # 0.0 to 1.0 for hue slider\n\n# Score system\nvar current_score: int = 0\n\n# Game state\nenum GameState {\n\tMENU,\n\tPLAYING,\n\tWIN,\n\tLOSE\n}\n\nvar current_game_state: GameState = GameState.MENU\n\n# Predefined ship colors for easy access\nvar ship_colors: Array[Color] = [\n\tColor.RED,        # Hue 0.0\n\tColor.ORANGE,     # Hue ~0.08\n\tColor.YELLOW,     # Hue ~0.17\n\tColor.GREEN,      # Hue ~0.33\n\tColor.CYAN,       # Hue ~0.5\n\tColor.BLUE,       # Hue ~0.67\n\tColor.MAGENTA,    # Hue ~0.83\n\tColor.WHITE       # Special case\n]\n\n@onready var Background: StarBackground\n\nfunc _ready() -> void:\n\t# Set initial ship color\n\tset_ship_color_from_hue(0.0)  # Start with red\n# Set ship color based on hue value (0.0 to 1.0)\nfunc set_ship_color_from_hue(hue_value: float) -> void:\n\tship_color_hue = clamp(hue_value, 0.0, 1.0)\n\t\n\t# Create color from HSV (<PERSON><PERSON>, Saturation, Value)\n\tship_color = Color.from_hsv(ship_color_hue, 1.0, 1.0)\n\t\n\t# Emit signal to notify other nodes\n\tship_color_changed.emit(ship_color)\n\n# Get current ship color\nfunc get_ship_color() -> Color:\n\treturn ship_color\n\n# Get current hue value for slider\nfunc get_ship_color_hue() -> float:\n\treturn ship_color_hue\n\n# Set game state\nfunc set_game_state(new_state: GameState) -> void:\n\tcurrent_game_state = new_state\n\n# Get current game state\nfunc get_game_state() -> GameState:\n\treturn current_game_state\n\n# Score management functions\nfunc add_score(points: int) -> void:\n\tcurrent_score += points\n\tscore_changed.emit(current_score)\n\nfunc get_score() -> int:\n\treturn current_score\n\nfunc reset_score() -> void:\n\tcurrent_score = 0\n\tscore_changed.emit(current_score)\n\n# Restart the game\nfunc restart_game() -> void:\n\treset_score()  # Reset score when restarting\n\tset_game_state(GameState.PLAYING)\n\tget_tree().change_scene_to_file(\"res://Scenes/Game.tscn\")\n\n# Go to main menu\nfunc go_to_main_menu() -> void:\n\tset_game_state(GameState.MENU)\n\tget_tree().change_scene_to_file(\"res://Scenes/UI/MainMenu.tscn\")\n\n# Show win screen\nfunc show_win_screen() -> void:\n\tset_game_state(GameState.WIN)\n\tget_tree().change_scene_to_file.call_deferred(\"res://Scenes/UI/WinScreen.tscn\")\n\n# Show lose screen\nfunc show_lose_screen() -> void:\n\tset_game_state(GameState.LOSE)\n\tget_tree().change_scene_to_file(\"res://Scenes/UI/LoseScreen.tscn\")\n", "exports": [], "functions": ["func _ready() -> void:", "func set_ship_color_from_hue(hue_value: float) -> void:", "func get_ship_color() -> Color:", "func get_ship_color_hue() -> float:", "func set_game_state(new_state: GameState) -> void:", "func get_game_state() -> GameState:", "func add_score(points: int) -> void:", "func get_score() -> int:", "func reset_score() -> void:", "func restart_game() -> void:", "func go_to_main_menu() -> void:", "func show_win_screen() -> void:", "func show_lose_screen() -> void:"], "language": "gd", "path": "res:///Scripts/GameManager.gd", "signals": ["signal ship_color_changed(new_color: Color)", "signal score_changed(new_score: int)"]}, {"content": "extends Node\nclass_name GravityModifierComponent\n\n# Component that modifies gravity effects on the player\n\nvar gravity_multiplier: float = 1.0\nvar player: Player\n\nfunc _ready():\n\tplayer = get_parent() as Player\n\tif not player:\n\t\tpush_error(\"GravityModifierComponent must be a child of Player\")\n\t\treturn\n\n# This function will be called by planets when applying gravity\nfunc modify_gravity_force(original_force: Vector2) -> Vector2:\n\treturn original_force * gravity_multiplier\n", "exports": [], "functions": ["func _ready():", "func modify_gravity_force(original_force: Vector2) -> Vector2:"], "language": "gd", "path": "res:///Scripts/GravityModifierComponent.gd", "signals": []}, {"content": "extends ShopItem\nclass_name GravityModifierItem\n\n# Gravity Modifier shop item - allows player to increase or decrease gravity effects\n\nenum GravityType {\n\tINCREASE,\n\tDECREASE\n}\n\n@export var gravity_type: GravityType = GravityType.INCREASE\n@export var gravity_modifier: float = 0.2  # 20% change per purchase\n\nfunc _init():\n\tsetup_item()\n\t\nfunc setup_item():\n\tmax_purchases = 3  # Can be upgraded multiple times\n\t\n\tif gravity_type == GravityType.INCREASE:\n\t\titem_name = \"Gravity +\"\n\t\tdescription = \"Increase gravity effects for tighter orbits\"\n\t\tcost = 120\n\telse:\n\t\titem_name = \"Gravity -\"\n\t\tdescription = \"Decrease gravity effects for easier navigation\"\n\t\tcost = 120\n\t\nfunc apply_effect(player: Player) -> void:\n\t# Add or update gravity modifier component\n\tvar gravity_component = player.get_node_or_null(\"GravityModifierComponent\")\n\tif not gravity_component:\n\t\tgravity_component = preload(\"res://Scripts/GravityModifierComponent.gd\").new()\n\t\tgravity_component.name = \"GravityModifierComponent\"\n\t\tplayer.add_child(gravity_component)\n\t\n\t# Apply gravity modification\n\tif gravity_type == GravityType.INCREASE:\n\t\tgravity_component.gravity_multiplier += gravity_modifier\n\telse:\n\t\tgravity_component.gravity_multiplier -= gravity_modifier\n\t\n\t# Ensure multiplier doesn't go below a minimum value\n\tgravity_component.gravity_multiplier = max(gravity_component.gravity_multiplier, 0.1)\n\t\n\tvar effect_text = \"increased\" if gravity_type == GravityType.INCREASE else \"decreased\"\n\tprint(\"Gravity %s! Multiplier: %.2f\" % [effect_text, gravity_component.gravity_multiplier])\n\nfunc get_current_cost() -> int:\n\t# Increase cost with each purchase\n\treturn cost + (current_purchases * 60)\n", "exports": ["@export var gravity_type: GravityType = GravityType.INCREASE", "@export var gravity_modifier: float = 0.2  # 20% change per purchase"], "functions": ["func _init():", "func setup_item():", "func apply_effect(player: Player) -> void:", "func get_current_cost() -> int:"], "language": "gd", "path": "res:///Scripts/GravityModifierItem.gd", "signals": []}, {"content": "extends BasePlanet\nclass_name HomePlanet\n\n@onready var Surface: Area2D = $Sprite/HomeArea\n@export var StationSprite : Texture2D\n\n# Shop interaction variables\nvar player_in_shop_range: bool = false\nvar current_player: Player = null\n\n# Shop UI references\nvar shop_ui: ShopManager = null\nvar shop_prompt: ShopPrompt = null\n\n# Home planet is now just a regular planet for navigation\n# Win condition has been moved to collecting all collectables\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tSprite.texture = StationSprite\n\n\t# This connects to the Surface area signals for shop interaction\n\tif Surface:\n\t\tSurface.body_entered.connect(_on_shop_area_entered)\n\t\tSurface.body_exited.connect(_on_shop_area_exited)\n\n\t# This adds some test score for shop testing\n\tGameManager.add_score(500)\n\n# Called every frame. 'delta' is the elapsed time since the previous frame.\nfunc _process(_delta: float) -> void:\n\t# This checks for E key press when player is in range\n\tif player_in_shop_range and current_player and Input.is_action_just_pressed(\"interact\"):\n\t\tprint(\"E key pressed, opening shop\")\n\t\topen_shop()\n\n# Shop interaction functions\nfunc _on_shop_area_entered(body: Node2D) -> void:\n\tif body is Player:\n\t\tprint(\"Player entered shop area\")\n\t\tplayer_in_shop_range = true\n\t\tcurrent_player = body\n\t\tshow_shop_prompt()\n\nfunc _on_shop_area_exited(body: Node2D) -> void:\n\tif body is Player:\n\t\tprint(\"Player exited shop area\")\n\t\tplayer_in_shop_range = false\n\t\tcurrent_player = null\n\t\thide_shop_prompt()\n\nfunc show_shop_prompt() -> void:\n\tif not shop_prompt:\n\t\t# This creates shop prompt if it doesn't exist\n\t\tvar prompt_scene = preload(\"res://Scenes/UI/ShopPrompt.tscn\")\n\t\tshop_prompt = prompt_scene.instantiate()\n\t\tget_tree().current_scene.add_child(shop_prompt)\n\n\t# This shows the prompt at the home planet position\n\tif shop_prompt and current_player:\n\t\tvar camera = current_player.get_node(\"Camera2D\")\n\t\tshop_prompt.show_prompt(self, camera)\n\nfunc hide_shop_prompt() -> void:\n\tif shop_prompt:\n\t\tshop_prompt.hide_prompt()\n\nfunc open_shop() -> void:\n\tif not shop_ui:\n\t\t# This creates shop UI if it doesn't exist\n\t\tvar shop_scene = preload(\"res://Scenes/UI/Shop.tscn\")\n\t\tshop_ui = shop_scene.instantiate()\n\t\tget_tree().current_scene.add_child(shop_ui)\n\t\tshop_ui.shop_closed.connect(_on_shop_closed)\n\n\tif shop_ui and current_player:\n\t\tshop_ui.open_shop(current_player)\n\t\thide_shop_prompt()\n\nfunc close_shop() -> void:\n\tif shop_ui:\n\t\tshop_ui.close_shop()\n\nfunc _on_shop_closed() -> void:\n\t# This shows prompt again when shop is closed if player is still in range\n\tif player_in_shop_range:\n\t\tshow_shop_prompt()\n", "exports": ["@export var StationSprite : Texture2D"], "functions": ["func _ready() -> void:", "func _process(_delta: float) -> void:", "func _on_shop_area_entered(body: Node2D) -> void:", "func _on_shop_area_exited(body: Node2D) -> void:", "func show_shop_prompt() -> void:", "func hide_shop_prompt() -> void:", "func open_shop() -> void:", "func close_shop() -> void:", "func _on_shop_closed() -> void:"], "language": "gd", "path": "res:///Scripts/HomePlanet.gd", "signals": []}, {"content": "extends Control\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tpass\n\n# Called when Restart button is pressed\nfunc _on_restart_button_pressed() -> void:\n\tGameManager.restart_game()\n\n# Called when Menu button is pressed\nfunc _on_menu_button_pressed() -> void:\n\tGameManager.go_to_main_menu()\n", "exports": [], "functions": ["func _ready() -> void:", "func _on_restart_button_pressed() -> void:", "func _on_menu_button_pressed() -> void:"], "language": "gd", "path": "res:///Scripts/LoseScreen.gd", "signals": []}, {"content": "extends Node\nclass_name Ma<PERSON>tComponent\n\n# Component that attracts collectables to the player\n\nvar strength: float = 0.0\nvar range: float = 0.0\nvar player: Player\n\nfunc _ready():\n\tplayer = get_parent() as Player\n\tif not player:\n\t\tpush_error(\"MagnetComponent must be a child of Player\")\n\t\treturn\n\nfunc _physics_process(_delta):\n\tif not player or strength <= 0:\n\t\treturn\n\n\t# Find all collectables in range\n\tvar collectables = get_tree().get_nodes_in_group(\"collectables\")\n\n\tfor collectable in collectables:\n\t\tif not is_instance_valid(collectable):\n\t\t\tcontinue\n\n\t\tvar distance = player.global_position.distance_to(collectable.global_position)\n\n\t\tif distance <= range and distance > 0:\n\t\t\t# Calculate attraction force\n\t\t\tvar direction = (player.global_position - collectable.global_position).normalized()\n\t\t\tvar force_strength = strength * (range - distance) / range\n\n\t\t\t# Apply force to collectable - try different methods\n\t\t\tif collectable.has_method(\"apply_central_force\"):\n\t\t\t\tcollectable.apply_central_force(direction * force_strength)\n\t\t\telif collectable.has_method(\"apply_impulse\"):\n\t\t\t\tcollectable.apply_impulse(direction * force_strength * _delta)\n\t\t\telif collectable.has_method(\"set_global_position\"):\n\t\t\t\t# For non-physics collectables, move them directly\n\t\t\t\tvar move_amount = direction * force_strength * _delta * 0.01\n\t\t\t\tcollectable.global_position += move_amount\n", "exports": [], "functions": ["func _ready():", "func _physics_process(_delta):"], "language": "gd", "path": "res:///Scripts/MagnetComponent.gd", "signals": []}, {"content": "extends ShopItem\nclass_name MagnetItem\n\n# Magnet shop item - attracts collectables to the player\n\n@export var magnet_strength: float = 500.0\n@export var magnet_range: float = 200.0\n\nfunc _init():\n\titem_name = \"Magnet\"\n\tdescription = \"Attracts nearby collectables to your ship\"\n\tcost = 150\n\tmax_purchases = 3  # Can be upgraded\n\t\nfunc apply_effect(player: Player) -> void:\n\t# Add magnet component to player if it doesn't exist\n\tvar magnet_component = player.get_node_or_null(\"MagnetComponent\")\n\tif not magnet_component:\n\t\tmagnet_component = preload(\"res://Scripts/MagnetComponent.gd\").new()\n\t\tmagnet_component.name = \"MagnetComponent\"\n\t\tplayer.add_child(magnet_component)\n\t\n\t# Upgrade magnet strength and range\n\tmagnet_component.strength += magnet_strength\n\tmagnet_component.range += magnet_range\n\t\n\tprint(\"Magnet upgraded! Strength: %d, Range: %d\" % [magnet_component.strength, magnet_component.range])\n\nfunc get_current_cost() -> int:\n\t# Increase cost with each purchase\n\treturn cost + (current_purchases * 50)\n", "exports": ["@export var magnet_strength: float = 500.0", "@export var magnet_range: float = 200.0"], "functions": ["func _init():", "func apply_effect(player: Player) -> void:", "func get_current_cost() -> int:"], "language": "gd", "path": "res:///Scripts/MagnetItem.gd", "signals": []}, {"content": "extends Control\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tpass\n\n# Called when Start Game button is pressed\nfunc _on_start_button_pressed() -> void:\n\t# Start the game through GameManager\n\tGameManager.restart_game()\n\n# Called when Settings button is pressed\nfunc _on_settings_button_pressed() -> void:\n\t# Load the Settings scene\n\tget_tree().change_scene_to_file(\"res://Scenes/UI/Settings.tscn\")\n\n# Called when Exit button is pressed\nfunc _on_exit_button_pressed() -> void:\n\t# Quit the game\n\tget_tree().quit()\n", "exports": [], "functions": ["func _ready() -> void:", "func _on_start_button_pressed() -> void:", "func _on_settings_button_pressed() -> void:", "func _on_exit_button_pressed() -> void:"], "language": "gd", "path": "res:///Scripts/MainMenu.gd", "signals": []}, {"content": "extends Control\nclass_name ObjectivesPanel\n\n# Objective tracking\nvar objectives: Dictionary = {}\nvar objective_labels: Dictionary = {}\n\n# Node references\n@onready var objectives_container: VBoxContainer = $VBoxContainer\n@onready var title_label: Label = $VBoxContainer/TitleLabel\n\nfunc _ready() -> void:\n\t# Initialize objectives based on collectables in the scene\n\tcall_deferred(\"scan_collectables_and_setup_objectives\")\n\nfunc scan_collectables_and_setup_objectives() -> void:\n\t# Count collectables by type in the scene\n\tvar collectable_counts: Dictionary = {}\n\t\n\t# Find all collectables in the scene\n\tvar collectables = get_tree().get_nodes_in_group(\"collectables\")\n\tif collectables.is_empty():\n\t\t# Fallback: search for Collectable nodes\n\t\tcollectables = find_all_collectables(get_tree().current_scene)\n\t\n\t# Count each type\n\tfor collectable in collectables:\n\t\tif collectable is Collectable:\n\t\t\tvar type_name = collectable.collection_name\n\t\t\tif type_name in collectable_counts:\n\t\t\t\tcollectable_counts[type_name] += 1\n\t\t\telse:\n\t\t\t\tcollectable_counts[type_name] = 1\n\t\t\t\n\t\t\t# Connect to collection signal\n\t\t\tcollectable.collected.connect(_on_collectable_collected)\n\t\n\t# Set up objectives\n\tfor type_name in collectable_counts:\n\t\tvar total = collectable_counts[type_name]\n\t\tadd_objective(type_name, 0, total)\n\nfunc find_all_collectables(node: Node) -> Array:\n\tvar collectables = []\n\t\n\tif node is Collectable:\n\t\tcollectables.append(node)\n\t\n\tfor child in node.get_children(true):\n\t\tcollectables.append_array(find_all_collectables(child))\n\t\n\treturn collectables\n\nfunc add_objective(objective_name: String, current: int, target: int) -> void:\n\t# Store objective data\n\tobjectives[objective_name] = {\n\t\t\"current\": current,\n\t\t\"target\": target,\n\t\t\"completed\": false\n\t}\n\t\n\t# Create label for this objective\n\tvar objective_label = Label.new()\n\tobjective_label.text = \"%s: %d/%d\" % [objective_name, current, target]\n\t\n\t# Style the label\n\tvar label_settings = LabelSettings.new()\n\tlabel_settings.font_size = 16\n\tlabel_settings.outline_size = 2\n\tlabel_settings.outline_color = Color.BLACK\n\tobjective_label.label_settings = label_settings\n\t\n\t# Add to container\n\tobjectives_container.add_child(objective_label)\n\tobjective_labels[objective_name] = objective_label\n\nfunc update_objective(objective_name: String, new_current: int) -> void:\n\tif objective_name not in objectives:\n\t\treturn\n\t\n\tvar objective = objectives[objective_name]\n\tobjective[\"current\"] = new_current\n\t\n\t# Check if completed\n\tif new_current >= objective[\"target\"] and not objective[\"completed\"]:\n\t\tobjective[\"completed\"] = true\n\t\tprint(\"Objective completed: %s\" % objective_name)\n\t\n\t# Update label\n\tif objective_name in objective_labels:\n\t\tvar label = objective_labels[objective_name]\n\t\tlabel.text = \"%s: %d/%d\" % [objective_name, new_current, objective[\"target\"]]\n\t\t\n\t\t# Change color if completed\n\t\tif objective[\"completed\"]:\n\t\t\tlabel.modulate = Color.GREEN\n\t\telse:\n\t\t\tlabel.modulate = Color.WHITE\n\nfunc _on_collectable_collected(collectable: Collectable) -> void:\n\tvar type_name = collectable.collection_name\n\t\n\tif type_name in objectives:\n\t\tvar current = objectives[type_name][\"current\"]\n\t\tupdate_objective(type_name, current + 1)\n\nfunc get_completion_status() -> Dictionary:\n\tvar total_objectives = objectives.size()\n\tvar completed_objectives = 0\n\t\n\tfor objective_name in objectives:\n\t\tif objectives[objective_name][\"completed\"]:\n\t\t\tcompleted_objectives += 1\n\t\n\treturn {\n\t\t\"completed\": completed_objectives,\n\t\t\"total\": total_objectives,\n\t\t\"all_complete\": completed_objectives == total_objectives\n\t}\n", "exports": [], "functions": ["func _ready() -> void:", "func scan_collectables_and_setup_objectives() -> void:", "func find_all_collectables(node: Node) -> Array:", "func add_objective(objective_name: String, current: int, target: int) -> void:", "func update_objective(objective_name: String, new_current: int) -> void:", "func _on_collectable_collected(collectable: Collectable) -> void:", "func get_completion_status() -> Dictionary:"], "language": "gd", "path": "res:///Scripts/ObjectivesPanel.gd", "signals": []}, {"content": "extends Area2D\nclass_name BasePlanet\n\n# This exports a variable to the Godot editor, allowing to change it without code.\n@export var gravity_strength: float = 6000.0\n\n# This creates an array to store physics bodies that enter the gravity field.\nvar bodies_in_gravity_field: Array[RigidBody2D] = []\n@export var gravityCurve : Curve\n@onready var Sprite: Sprite2D = $CollisionShape2D/Sprite2D\n# Orbit detection variables\nvar player_orbit_data: Dictionary = {}  # Stores orbit tracking data for each player\n\n\nfunc _ready() -> void:\n\t# Add to planets group\n\tadd_to_group(\"planets\")\n\n\n# This function runs every physics frame.\nfunc _physics_process(_delta: float) -> void:\n\t# This loops through every body currently stored in the array.\n\tfor body in bodies_in_gravity_field:\n\t\t# This calculates the direction from the body towards this planet.\n\t\tif(not body.onPlanet):\n\t\t\tvar direction_to_planet = (global_position - body.global_position).normalized()\n\n\t\t# This calculates the force vector by combining direction and strength.\n\t\t\tvar gravity_force = direction_to_planet * gravity_strength * ((Sprite.texture.get_size().x/2) / to_local(global_position).distance_to(body.to_local(global_position))) # gravityCurve.sample\n\n\t\t\t# Check if body has gravity modifier component\n\t\t\tvar gravity_modifier = body.get_node_or_null(\"GravityModifierComponent\")\n\t\t\tif gravity_modifier:\n\t\t\t\tgravity_force = gravity_modifier.modify_gravity_force(gravity_force)\n\n\t\t# This applies the calculated force to the center of the body.\n\t\t\tbody.apply_central_force(gravity_force)\n\n\t\t# Track orbit for players\n\t\tif body is Player:\n\t\t\ttrack_player_orbit(body)\n\t\t\n\t\n\n# This function runs when a body enters the Area2D's collision shape.\nfunc _on_body_entered(body: Node2D) -> void:\n\t# This checks if the entering node is a RigidBody2D.\n\tif body is RigidBody2D:\n\t\t# This checks if the body is not already in the tracking array.\n\t\tif not body in bodies_in_gravity_field:\n\t\t\t# This adds the body to the array so gravity will affect it.\n\t\t\tbodies_in_gravity_field.append(body)\n\n\t\t\t# Initialize orbit tracking for players\n\t\t\tif body is Player:\n\t\t\t\tinitialize_orbit_tracking(body)\n\n\n# This function runs when a body exits the Area2D's collision shape.\nfunc _on_body_exited(body: Node2D) -> void:\n\t# This checks if the exiting body is in the tracking array.\n\tif body is not RigidBody2D:\n\t\treturn\n\t\t\n\tif body in bodies_in_gravity_field:\n\t\t# This removes the body from the array, stopping the gravity effect.\n\t\tbodies_in_gravity_field.erase(body)\n\n\t\t# Clean up orbit tracking for players\n\t\tif body is Player and body in player_orbit_data:\n\t\t\tplayer_orbit_data.erase(body)\n\n# Initialize orbit tracking for a player\nfunc initialize_orbit_tracking(player: Player) -> void:\n\tvar initial_angle = get_angle_to_player(player)\n\tplayer_orbit_data[player] = {\n\t\t\"last_angle\": initial_angle,\n\t\t\"total_rotation\": 0.0,\n\t\t\"has_completed_orbit\": false,\n\t\t\"last_orbit_time\": 0.0  # Prevent rapid orbit scoring\n\t}\n\n# Track player's orbit around this planet\nfunc track_player_orbit(player: Player) -> void:\n\tif not player in player_orbit_data:\n\t\tinitialize_orbit_tracking(player)\n\t\treturn\n\n\tvar current_angle = get_angle_to_player(player)\n\tvar orbit_data = player_orbit_data[player]\n\tvar last_angle = orbit_data[\"last_angle\"]\n\n\t# Calculate angle difference, handling wrap-around\n\tvar angle_diff = current_angle - last_angle\n\tif angle_diff > PI:\n\t\tangle_diff -= 2 * PI\n\telif angle_diff < -PI:\n\t\tangle_diff += 2 * PI\n\n\t# Update total rotation\n\torbit_data[\"total_rotation\"] += angle_diff\n\torbit_data[\"last_angle\"] = current_angle\n\n\t# Check if player completed a full orbit (360 degrees = 2π radians)\n\tvar current_time = Time.get_time_dict_from_system()[\"second\"]\n\tif abs(orbit_data[\"total_rotation\"]) >= 2 * PI and not orbit_data[\"has_completed_orbit\"]:\n\t\t# Prevent rapid orbit scoring (minimum 2 seconds between orbits)\n\t\tif current_time - orbit_data[\"last_orbit_time\"] >= 2.0:\n\t\t\torbit_data[\"has_completed_orbit\"] = true\n\t\t\torbit_data[\"last_orbit_time\"] = current_time\n\t\t\taward_orbit_score(player)\n\t\t\t# Reset for potential multiple orbits\n\t\t\torbit_data[\"total_rotation\"] = 0.0\n\t\t\torbit_data[\"has_completed_orbit\"] = false\n\n# Get angle from planet center to player\nfunc get_angle_to_player(player: Player) -> float:\n\tvar direction = player.global_position - global_position\n\treturn direction.angle()\n\n# Award score for completing an orbit\nfunc award_orbit_score(player: Player) -> void:\n\tGameManager.add_score(100)\n\tplayer.BoostCount += 1\n\tprint(\"Player completed orbit around planet! +100 points\")\n", "exports": ["@export var gravity_strength: float = 6000.0", "@export var gravityCurve : Curve"], "functions": ["func _ready() -> void:", "func _physics_process(_delta: float) -> void:", "func _on_body_entered(body: Node2D) -> void:", "func _on_body_exited(body: Node2D) -> void:", "func initialize_orbit_tracking(player: Player) -> void:", "func track_player_orbit(player: Player) -> void:", "func get_angle_to_player(player: Player) -> float:", "func award_orbit_score(player: Player) -> void:"], "language": "gd", "path": "res:///Scripts/planet.gd", "signals": []}, {"content": "extends RigidBody2D\nclass_name Player\n\n# This defines a set of named states for the player's state machine.\nenum State {\n\tREADY_TO_AIM,\n\tAIMING,\n\tLAUNCHED\n}\n\n# A cooldown for launching. TTDG: I use it to make sure releasing fast doesn't use a boost.\nconst LAUNCH_COOLDOWN_TIME : float = 0.3\n# yeah that (half a second)\nvar canBoost : bool = false\n\n# The particles\n@onready var _LaunchParticles: ParticleEffect = $LaunchParticles\n@onready var _BoostParticles: ParticleEffect = $BoostParticles\n# The sprite\n@onready var Sprite: Sprite2D = $Sprite2D\n# The Camera\n@onready var camera_2d: ScreenShake = $Camera2D\n\n# This exports a variable for launch power, tunable in the Inspector.\n@export var launch_power: float = 10.0\n# This exports a variable for the maximum drag distance (100% power).\n@export var max_pull_distance: float = 200.0\n# This exports a variable for the boost impulse strength.\n@export var boost_strength: float = 1000.0\n\n#defines how fast a click should be. I use 0.2 in another game just fine.\nconst CLICK_TIME : float = 0.2\n\n# Flag to double launch if on planet\nvar onPlanet : bool = false\n\nstatic var Position : Vector2\n# This variable will hold the player's current state from the enum above.\nvar current_state: State = State.READY_TO_AIM\n# This boolean tracks if the one-time boost is still available.\nvar BoostCount: int = 1\n\n# This new variable will store the calculated pull vector while aiming.\nvar _current_aim_pull_vector: Vector2 = Vector2.ZERO\n\n#stores whether a single touch is happening\nvar SingleTouchDown : bool = false\n\n# Lose condition variables\nstatic var max_distance_from_origin: float = 15000.0  # Maximum distance before losing\nstatic var origin_position: Vector2 = Vector2.ZERO\nvar has_lost: bool = false\n\n# This creates a reference to the Line2D node for drawing the power bar.\n@onready var line_2d: Line2D = $Line2D\n# This creates a reference to the Sprite2D node.\n@onready var sprite: Sprite2D = $Sprite2D\n\nfunc _ready() -> void:\n\t\n\tlinear_damp_mode = RigidBody2D.DAMP_MODE_COMBINE\n\t# Store starting position as origin\n\torigin_position = global_position\n\t# Apply ship color from GameManager\n\tapply_ship_color()\n\t# Connect to color change signal\n\tGameManager.ship_color_changed.connect(_on_ship_color_changed)\n\n# Apply the current ship color from GameManager\nfunc apply_ship_color() -> void:\n\t#var ship_color = GameManager.get_ship_color()\n\t#Sprite.modulate = ship_color\n\tpass\n\t\n# Called when ship color changes in GameManager\nfunc _on_ship_color_changed(_new_color: Color) -> void:\n\tapply_ship_color()\n\n# Check if player has gone too far and should lose\nfunc check_lose_condition() -> void:\n\tif has_lost:\n\t\treturn\n\n\tvar distance_from_origin = global_position.distance_to(origin_position)\n\tif distance_from_origin > max_distance_from_origin:\n\t\thas_lost = true\n\t\tprint(\"Player went too far! Distance: \", distance_from_origin)\n\t\t\n\t\t# Show lose screen after a short delay\n\t\t#await get_tree().create_timer(1.0).timeout\n\t\t#Actually don't lol\n\t\tGameManager.show_lose_screen()\n\n# This function is called by Godot when an input event occurs on this object.\nfunc _input(ev: InputEvent) -> void:\n\tif ev is InputEventScreenDrag:\n\t\tif ev.index == 0 and ev.is_pressed():\n\t\t\tSingleTouchDown = true\n\t\telif ev.index == 0 and not ev.is_pressed():\n\t\t\tSingleTouchDown = false\n\tif ev is InputEventMouseButton:\n\t\tvar event = ev as InputEventMouseButton\n\t\tif event.pressed and event.button_index == MOUSE_BUTTON_LEFT:\n\t\t\tclickTimer = get_tree().create_timer(CLICK_TIME)\n\t\t\t\n# a timer to check if the mouse button was down/up quick\nvar clickTimer : SceneTreeTimer\n\n# This function is called every frame.\nfunc _process(_delta: float) -> void:\n\t# Don't process input if game is paused (shop is open)\n\tif get_tree().paused:\n\t\treturn\n\n\t#change the offset position of the background so it looks more like you're moving\n\tPosition = global_position\n\n\t#Reset whether ship can aim\n\tif (Input.is_action_just_pressed(\"DEBUG-RESET_LAUNCH\")):\n\t\tReset()\n\t\n\t\n\t# This handles only the left mouse button events.\n\tif Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT):\n\t\t# This starts aiming only when pressed and only from READY_TO_AIM.\n\t\tif current_state == State.READY_TO_AIM:\n\t\t\tcurrent_state = State.AIMING\n\t\t\t# Immediately update aim line for visual feedback.\n\t\t\tupdate_aim_line()\n\t\t\t\n\n\t\t\t\n\t\t# This launches on mouse release while AIMING.\n\n\t# This updates the aim line only while AIMING and the mouse button is held.\n\tif current_state == State.AIMING and (Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT) or SingleTouchDown):\n\t\t# This updates the aim line visuals and _current_aim_pull_vector.\n\t\tupdate_aim_line()\n\n\t\t\n\t\t# This makes the ship face the mouse cursor while aiming.\n\t\tvar mouse_position = to_local(global_position) - to_local(get_global_mouse_position())\n\t\tlook_at(to_global(mouse_position))\n\t\n\t# This checks if the player is aiming and spacebar is pressed to launch.\n\t# Note: This is separate from mouse release to allow \"set and shoot\" with space.\n\tif current_state == State.AIMING and not (Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT) or SingleTouchDown):\n\t\t# This calls the function to launch the player using the stored aim vector.\n\t\t#if(sleeping):\n\t\t\t#set_deferred(\"sleeping\", false)\n\t\t\t#onPlanet = true\n\t\tlaunch()\n\t\t\n\tif(not Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT)):\n\t\tif clickTimer and clickTimer.time_left > 0 and BoostCount > 0 and canBoost:\n\t\t\tapply_boost()\n\t\t\tclickTimer = null\n\n# This function runs every physics frame, ideal for physics-related code.\nfunc _physics_process(delta: float) -> void:\n\t# Don't process physics input if game is paused (shop is open)\n\tif get_tree().paused:\n\t\treturn\n\n\t# Debug movement (assuming \"DEBUG-*\" inputs are set up) || WASD\n\tglobal_position += Vector2(Input.get_axis(\"DEBUG-LEFT\", \"DEBUG-RIGHT\"), Input.get_axis(\"DEBUG-UP\", \"DEBUG-DOWN\")) * 1000 * delta\n\n\t# Check lose condition - if player is too far from origin\n\tcheck_lose_condition()\n\tif is_inside_tree():\n\t\tvar collision : KinematicCollision2D = move_and_collide(linear_velocity.normalized(), true)\n\t\tif(collision):\n\t\t\tvar collider = collision.get_collider()\n\t\t\tif collider.owner is BasePlanet:\n\t\t\t\tif(!onPlanet):\n\t\t\t\t\tprint(\"onPlanet\")\n\t\t\t\t\tlinear_velocity = Vector2.ZERO\n\t\t\t\t\tangular_velocity = 0.0\n\t\t\t\t\t#set_deferred(\"sleeping\", true)\n\t\t\t\t\tReset()\n\t\t\t\tonPlanet = true\n\n\t# This checks if the player is in the air.\n\tif current_state == State.LAUNCHED:\n\t\t# This makes the rocket point in the direction it's moving.\n\t\tif linear_velocity.length() > 0.01: # Avoid rotation issues if velocity is zero\n\t\t\trotation = linear_velocity.angle()\n\t\t# This checks if the boost is available and the user pressed boost.\n\t\tif BoostCount > 0 and Input.is_action_just_pressed(\"boost\"):\n\t\t\tapply_boost()\n\t\tif(Input.is_action_pressed(\"brake\")):\n\t\t\tlinear_damp = 5\n\t\telse:\n\t\t\tlinear_damp = 0\n# This function handles the logic for launching the player.\nfunc launch() -> void:\n\t\n\tget_tree().create_timer(LAUNCH_COOLDOWN_TIME).timeout.connect(func(): canBoost=true)\n\tcanBoost = false\n\t# Use the pre-calculated and stored aim vector.\n\tvar final_pull_vector = _current_aim_pull_vector\n\t\n\t# This sets the player's initial velocity based on the stored pull vector and launch power.\n\tlinear_velocity = final_pull_vector * launch_power * (2.0 if onPlanet else 1.0)\n\t\n\t# This changes the state to LAUNCHED.\n\tcurrent_state = State.LAUNCHED\n\t# This makes the RigidBody no longer clickable after launch.\n\tset_pickable(false)\n\t# This clears the aiming line from the screen.\n\tline_2d.clear_points()\n\t\n\t_LaunchParticles.Emit()\n\t\n\t#If you're on the planet and boost, you're getting off the planet, but it needs to happen AFTER the math is done.\n\tif(onPlanet):\n\t\tprint(\"OffPlanet\")\n\t\t\n\t\tonPlanet = false\n\t\t\t\n\t# TODO: Add a sound for the boost here!\n\n# This function applies the one-time boost.\nfunc apply_boost() -> void:\n\t# This gets the forward direction of the rocket.\n\tvar boost_direction = Vector2.RIGHT.rotated(rotation)\n\t# This applies an instant force (impulse) in the forward direction.\n\tapply_central_impulse(boost_direction * boost_strength)\n\t# This consumes the boost so it cannot be used again.\n\tBoostCount -= 1\n\t\n\t# This provides visual feedback that the boost was used.\n\t#sprite.modulate = Color.CYAN\n\t_BoostParticles.Emit(true)\n\tif(BoostCount == 0):\n\t\tSprite.frame_coords.y = 1\n\t\t$\"BoostParticles-Explosion\".Emit(true)\n\tcamera_2d.Shake()\n\t\n\n\t# TODO: Add a sound for the boost here!\n\n# This function draws and updates the aiming line.\nfunc update_aim_line() -> void:\n\t# This calculates the global vector from the player's current position to the current mouse position.\n\tvar pull_vector_from_player_to_mouse = global_position - get_global_mouse_position()\n\t# This clamps the vector's length to the max_pull_distance.\n\t_current_aim_pull_vector = pull_vector_from_player_to_mouse.limit_length(max_pull_distance)\n\t\n\t# This calculates the power percentage (0.0 to 1.0) based on distance.\n\tvar power_percentage = _current_aim_pull_vector.length() / max_pull_distance\n\t\n\t# This clears any previous points from the line.\n\tline_2d.clear_points()\n\t# This adds a point at the player's center (0,0 in local coordinates for the Line2D).\n\tline_2d.add_point(Vector2.ZERO)\n\t\n\t# This adds the end point of the pull vector, transformed into Line2D's local space.\n\t# It ensures the line points correctly regardless of player's current rotation.\n\tline_2d.add_point(global_transform.basis_xform_inv(_current_aim_pull_vector))\n\t\n\t# This calculates the color interpolation from white to red based on power.\n\tvar line_color = Color.WHITE.lerp(Color.RED, power_percentage)\n\tline_2d.default_color = line_color\n\t\n\t# This sets the line width based on power (thicker line = more power).\n\tline_2d.width = 3.0 + power_percentage * 7.0\n\n\nfunc Reset():\n\tSprite.frame_coords.y = 0\n\tcurrent_state = State.READY_TO_AIM\n\n\t# Reset boost count to starting amount (including shop upgrades)\n\tif has_meta(\"starting_boosts\"):\n\t\tBoostCount = get_meta(\"starting_boosts\")\n\telse:\n\t\tBoostCount = 1\n", "exports": ["@export var launch_power: float = 10.0", "@export var max_pull_distance: float = 200.0", "@export var boost_strength: float = 1000.0"], "functions": ["func _ready() -> void:", "func apply_ship_color() -> void:", "func _on_ship_color_changed(_new_color: Color) -> void:", "func check_lose_condition() -> void:", "func _input(ev: InputEvent) -> void:", "func _process(_delta: float) -> void:", "func _physics_process(delta: float) -> void:", "func launch() -> void:", "func apply_boost() -> void:", "func update_aim_line() -> void:", "func Reset():"], "language": "gd", "path": "res:///Scripts/player.gd", "signals": []}, {"content": "extends Sprite2D\n@export var PlanetSprites : Array[Texture2D]\n\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\ttexture = PlanetSprites.pick_random()\n\trotation_degrees = randi()%360\n\tpass # Replace with function body.\n\n\n# Called every frame. 'delta' is the elapsed time since the previous frame.\nfunc _process(delta: float) -> void:\n\tpass\n", "exports": ["@export var PlanetSprites : Array[Texture2D]"], "functions": ["func _ready() -> void:", "func _process(delta: float) -> void:"], "language": "gd", "path": "res:///Scripts/rand_sprite.gd", "signals": []}, {"content": "extends Control\n@onready var color_slider: HSlider = $Background/Panel/VBoxContainer/ColorSliderContainer/ColorSlider\n@onready var example_ship: Sprite2D = $Background/Panel/VBoxContainer/ExampleShipContainer/ExampleShip\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\t# Set initial slider value from GameManager\n\tcolor_slider.value = GameManager.get_ship_color_hue()\n\t# Update example ship color\n\tupdate_example_ship_color()\n\n\t# Connect to GameManager signal for color changes\n\tGameManager.ship_color_changed.connect(_on_ship_color_changed)\n\n# Called when the color slider value changes\nfunc _on_color_slider_value_changed(value: float) -> void:\n\t# Update GameManager with new hue value\n\tGameManager.set_ship_color_from_hue(value)\n\n# Called when GameManager ship color changes\nfunc _on_ship_color_changed(new_color: Color) -> void:\n\tupdate_example_ship_color()\n\n# Update the example ship sprite color\nfunc update_example_ship_color() -> void:\n\tvar ship_color = GameManager.get_ship_color()\n\texample_ship.modulate = ship_color\n\n# Called when Back button is pressed\nfunc _on_back_button_pressed() -> void:\n\t# Return to main menu\n\tGameManager.go_to_main_menu()\n", "exports": [], "functions": ["func _ready() -> void:", "func _on_color_slider_value_changed(value: float) -> void:", "func _on_ship_color_changed(new_color: Color) -> void:", "func update_example_ship_color() -> void:", "func _on_back_button_pressed() -> void:"], "language": "gd", "path": "res:///Scripts/Settings.gd", "signals": []}, {"content": "extends Resource\nclass_name ShopItem\n\n# Base class for all shop items\n\n@export var item_name: String = \"\"\n@export var description: String = \"\"\n@export var cost: int = 0\n@export var icon: Texture2D = null\n@export var is_purchased: bool = false\n@export var max_purchases: int = 1  # -1 for unlimited\n@export var current_purchases: int = 0\n\n# Virtual function to be overridden by specific items\nfunc apply_effect(player: Player) -> void:\n\tpush_error(\"apply_effect() must be implemented by subclass\")\n\n# Check if item can be purchased\nfunc can_purchase() -> bool:\n\tif is_purchased and max_purchases == 1:\n\t\treturn false\n\tif max_purchases > 0 and current_purchases >= max_purchases:\n\t\treturn false\n\treturn GameManager.get_score() >= cost\n\n# Purchase the item\nfunc purchase(player: Player) -> bool:\n\tif not can_purchase():\n\t\treturn false\n\t\n\t# Deduct cost\n\tGameManager.add_score(-cost)\n\t\n\t# Apply effect\n\tapply_effect(player)\n\t\n\t# Update purchase state\n\tcurrent_purchases += 1\n\tif max_purchases == 1:\n\t\tis_purchased = true\n\t\n\treturn true\n\n# Get display text for purchase button\nfunc get_purchase_text() -> String:\n\tif is_purchased and max_purchases == 1:\n\t\treturn \"PURCHASED\"\n\telif max_purchases > 1:\n\t\treturn \"BUY (%d/%d)\" % [current_purchases, max_purchases]\n\telse:\n\t\treturn \"BUY - %d pts\" % cost\n\n# Get the current cost (might increase with multiple purchases)\nfunc get_current_cost() -> int:\n\treturn cost\n", "exports": ["@export var item_name: String = \"\"", "@export var description: String = \"\"", "@export var cost: int = 0", "@export var icon: Texture2D = null", "@export var is_purchased: bool = false", "@export var max_purchases: int = 1  # -1 for unlimited", "@export var current_purchases: int = 0"], "functions": ["func apply_effect(player: Player) -> void:", "func can_purchase() -> bool:", "func purchase(player: Player) -> bool:", "func get_purchase_text() -> String:", "func get_current_cost() -> int:"], "language": "gd", "path": "res:///Scripts/ShopItem.gd", "signals": []}, {"content": "extends CanvasLayer\nclass_name ShopManager\n\n# Shop UI Manager\n\n@onready var shop_panel: Panel = $Control/ShopPanel\n@onready var items_container: VBoxContainer = $Control/ShopPanel/VBoxContainer/ScrollContainer/ItemsContainer\n@onready var score_label: Label = $Control/ShopPanel/VBoxContainer/HeaderContainer/ScoreLabel\n@onready var close_button: Button = $Control/ShopPanel/VBoxContainer/HeaderContainer/CloseButton\n@onready var title_label: Label = $Control/ShopPanel/VBoxContainer/HeaderContainer/TitleLabel\n\n# Shop items\nvar shop_items: Array[ShopItem] = []\nvar current_player: Player = null\n\n# Signals\nsignal shop_closed\n\nfunc _ready():\n\t# This hides shop initially\n\tvisible = false\n\t\n\t# This connects close button\n\tif close_button:\n\t\tclose_button.pressed.connect(close_shop)\n\t\n\t# This initializes shop items\n\tinitialize_shop_items()\n\t\n\t# This connects to score changes\n\tGameManager.score_changed.connect(_on_score_changed)\n\nfunc initialize_shop_items():\n\t# This creates shop items TODO Add more\n\tvar magnet_item = MagnetItem.new()\n\tvar boosts_item = StartingBoostsItem.new()\n\tvar gravity_plus_item = GravityModifierItem.new()\n\tgravity_plus_item.gravity_type = GravityModifierItem.GravityType.INCREASE\n\tgravity_plus_item.setup_item()\n\t\n\tvar gravity_minus_item = GravityModifierItem.new()\n\tgravity_minus_item.gravity_type = GravityModifierItem.GravityType.DECREASE\n\tgravity_minus_item.setup_item()\n\t\n\tshop_items = [magnet_item, boosts_item, gravity_plus_item, gravity_minus_item]\n\t\n\t# This creates UI for each item\n\tcreate_shop_ui()\n\nfunc create_shop_ui():\n\t# This clears existing items\n\tfor child in items_container.get_children():\n\t\tchild.queue_free()\n\t\n\t# This creates UI for each shop item\n\tfor item in shop_items:\n\t\tvar item_ui = create_item_ui(item)\n\t\titems_container.add_child(item_ui)\n\nfunc create_item_ui(item: ShopItem) -> Control:\n\tvar item_container = HBoxContainer.new()\n\titem_container.custom_minimum_size = Vector2(0, 60)\n\t\n\t# Item info container\n\tvar info_container = VBoxContainer.new()\n\tinfo_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL\n\t\n\t# Item name\n\tvar name_label = Label.new()\n\tname_label.text = item.item_name\n\tname_label.add_theme_font_size_override(\"font_size\", 16)\n\tinfo_container.add_child(name_label)\n\t\n\t# Item description\n\tvar desc_label = Label.new()\n\tdesc_label.text = item.description\n\tdesc_label.add_theme_font_size_override(\"font_size\", 12)\n\tdesc_label.modulate = Color(0.8, 0.8, 0.8)\n\tinfo_container.add_child(desc_label)\n\t\n\t# Cost label\n\tvar cost_label = Label.new()\n\tcost_label.text = \"Cost: %d pts\" % item.get_current_cost()\n\tcost_label.add_theme_font_size_override(\"font_size\", 12)\n\tinfo_container.add_child(cost_label)\n\t\n\titem_container.add_child(info_container)\n\t\n\t# Purchase button\n\tvar purchase_button = Button.new()\n\tpurchase_button.text = item.get_purchase_text()\n\tpurchase_button.custom_minimum_size = Vector2(120, 50)\n\tpurchase_button.disabled = not item.can_purchase()\n\t\n\t# Connect purchase button\n\tpurchase_button.pressed.connect(func(): purchase_item(item, purchase_button, cost_label))\n\t\n\titem_container.add_child(purchase_button)\n\t\n\treturn item_container\n\nfunc open_shop(player: Player):\n\tcurrent_player = player\n\tvisible = true\n\tget_tree().paused = true\n\tupdate_shop_display()\n\nfunc close_shop():\n\tvisible = false\n\tget_tree().paused = false\n\tshop_closed.emit()\n\nfunc purchase_item(item: ShopItem, button: Button, cost_label: Label):\n\tif not current_player:\n\t\treturn\n\t\n\tif item.purchase(current_player):\n\t\t# Update button and cost display\n\t\tbutton.text = item.get_purchase_text()\n\t\tbutton.disabled = not item.can_purchase()\n\t\tcost_label.text = \"Cost: %d pts\" % item.get_current_cost()\n\t\t\n\t\t# Update score display\n\t\tupdate_score_display()\n\t\t\n\t\tprint(\"Purchased: %s\" % item.item_name)\n\telse:\n\t\tprint(\"Cannot purchase: %s\" % item.item_name)\n\nfunc update_shop_display():\n\tupdate_score_display()\n\t\n\t# Update all item UIs\n\tvar item_uis = items_container.get_children()\n\tfor i in range(min(shop_items.size(), item_uis.size())):\n\t\tvar item = shop_items[i]\n\t\tvar item_ui = item_uis[i]\n\t\t\n\t\t# Update button state\n\t\tvar button = item_ui.get_child(1) as Button\n\t\tif button:\n\t\t\tbutton.text = item.get_purchase_text()\n\t\t\tbutton.disabled = not item.can_purchase()\n\t\t\n\t\t# Update cost label\n\t\tvar info_container = item_ui.get_child(0)\n\t\tvar cost_label = info_container.get_child(2) as Label\n\t\tif cost_label:\n\t\t\tcost_label.text = \"Cost: %d pts\" % item.get_current_cost()\n\nfunc update_score_display():\n\tif score_label:\n\t\tscore_label.text = \"Score: %d\" % GameManager.get_score()\n\nfunc _on_score_changed(new_score: int):\n\tupdate_score_display()\n\t\n\t# Update button states when score changes\n\tvar item_uis = items_container.get_children()\n\tfor i in range(min(shop_items.size(), item_uis.size())):\n\t\tvar item = shop_items[i]\n\t\tvar item_ui = item_uis[i]\n\t\tvar button = item_ui.get_child(1) as Button\n\t\tif button:\n\t\t\tbutton.disabled = not item.can_purchase()\n\nfunc _input(event):\n\tif visible and (event.is_action_pressed(\"ui_cancel\") or event.is_action_pressed(\"interact\")):\n\t\tclose_shop()\n", "exports": [], "functions": ["func _ready():", "func initialize_shop_items():", "func create_shop_ui():", "func create_item_ui(item: ShopItem) -> Control:", "func open_shop(player: Player):", "func close_shop():", "func purchase_item(item: ShopItem, button: Button, cost_label: Label):", "func update_shop_display():", "func update_score_display():", "func _on_score_changed(new_score: int):", "func _input(event):"], "language": "gd", "path": "res:///Scripts/ShopManager.gd", "signals": ["signal shop_closed"]}, {"content": "extends CanvasLayer\nclass_name Shop<PERSON>rompt\n\n# Floating E prompt that appears when player can interact with shop\n\n@onready var control_node: Control = $Control\n@onready var prompt_label: Label = $Control/PromptLabel\n@onready var animation_player: AnimationPlayer = $Control/AnimationPlayer\n\nvar target_node: Node2D\nvar camera: Camera2D\n\nfunc _ready():\n\t# This hides initially\n\tvisible = false\n\n\t# This sets up the prompt\n\tif prompt_label:\n\t\tprompt_label.text = \"E\"\n\t\tprompt_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER\n\t\tprompt_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER\n\nfunc show_prompt(target: Node2D, camera_ref: Camera2D):\n\ttarget_node = target\n\tcamera = camera_ref\n\tvisible = true\n\n\t# This starts floating animation\n\tif animation_player and animation_player.has_animation(\"float\"):\n\t\tanimation_player.play(\"float\")\n\nfunc hide_prompt():\n\tvisible = false\n\tif animation_player:\n\t\tanimation_player.stop()\n\nfunc _process(_delta):\n\tif visible and target_node and camera and control_node:\n\t\t# This converts world position to screen position using the camera\n\t\tvar world_pos = target_node.global_position\n\t\tvar screen_pos = camera.get_screen_center_position() + (world_pos - camera.global_position)\n\t\tcontrol_node.global_position = screen_pos + Vector2(-5, -10)  # This positions 5px left and 10px up from target\n", "exports": [], "functions": ["func _ready():", "func show_prompt(target: Node2D, camera_ref: Camera2D):", "func hide_prompt():", "func _process(_delta):"], "language": "gd", "path": "res:///Scripts/ShopPrompt.gd", "signals": []}, {"content": "extends ShopItem\nclass_name StartingBoostsItem\n\n# Starting Boosts shop item - increases the number of boosts the player starts with\n\n@export var boost_increase: int = 1\n\nfunc _init():\n\titem_name = \"Starting Boosts\"\n\tdescription = \"Start each launch with additional boost charges\"\n\tcost = 100\n\tmax_purchases = 5  # Can be upgraded multiple times\n\t\nfunc apply_effect(player: Player) -> void:\n\t# Increase the player's starting boost count\n\tplayer.BoostCount += boost_increase\n\t\n\t# Also add a property to track starting boosts for respawn\n\tif not player.has_meta(\"starting_boosts\"):\n\t\tplayer.set_meta(\"starting_boosts\", 1)\n\t\n\tvar starting_boosts = player.get_meta(\"starting_boosts\") + boost_increase\n\tplayer.set_meta(\"starting_boosts\", starting_boosts)\n\t\n\tprint(\"Starting boosts increased! Now starting with %d boosts\" % starting_boosts)\n\nfunc get_current_cost() -> int:\n\t# Increase cost with each purchase\n\treturn cost + (current_purchases * 75)\n", "exports": ["@export var boost_increase: int = 1"], "functions": ["func _init():", "func apply_effect(player: Player) -> void:", "func get_current_cost() -> int:"], "language": "gd", "path": "res:///Scripts/StartingBoostsItem.gd", "signals": []}, {"content": "extends Control\nclass_name StarBackground\n\n\t\nfunc _process(_delta: float) -> void:\n\tmaterial.set(\"shader_parameter/offset\", Player.Position)\n\tvar differenceBetweenShipPosAndMaxDistance : float = abs(Player.max_distance_from_origin - Player.Position.distance_to(Player.origin_position))\n\n\tif differenceBetweenShipPosAndMaxDistance < 1500:\n\t\tvar bgc : Color = material.get(\"shader_parameter/bg_color\")\n\t\tvar red : float = clamp(1.0 - (differenceBetweenShipPosAndMaxDistance / 1500.0), 0.0, 0.33)\n\t\tmaterial.set(\"shader_parameter/bg_color\", Color(abs(red), bgc.g, bgc.b))\n\telse:\n\t\tmaterial.set(\"shader_parameter/bg_color\", Color(0,0,0))\n\t\t\n\t#material.set(\"shader_parameter/paralax\", amount)\n\treturn\n\n\t\n", "exports": [], "functions": ["func _process(_delta: float) -> void:"], "language": "gd", "path": "res:///Scripts/star_background.gd", "signals": []}, {"content": "@tool\nextends Node2D\nconst PLANET_LARGE = preload(\"res://Scenes/planet_large.tscn\")\nconst PLANET_MEDIUM = preload(\"res://Scenes/planet_medium.tscn\")\nconst PLANET_SMALL = preload(\"res://Scenes/planet_small.tscn\")\n\nvar planets : Array = [PLANET_LARGE, PLANET_MEDIUM, PLANET_SMALL]\n@export var active : bool = false\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\t\n\tpass # Replace with function body.\n\nvar placing = false\n# Called every frame. 'delta' is the elapsed time since the previous frame.\nfunc _process(delta: float) -> void:\n\tif Engine.is_editor_hint():\n\t\tif not active:\n\t\t\treturn\n\t\tif(Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT) and not placing):\n\t\t\tprint(\"Editor click!\")\n\t\t\tvar planetToAdd = planets.pick_random().instantiate()\n\t\t\tplanetToAdd.global_position = get_global_mouse_position()\n\t\t\t$\".\".add_child(planetToAdd)\n\t\t\tplanetToAdd.owner = self\n\t\t\tplacing = true\n\t\t\t\n\t\telif(not Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT)):\n\t\t\tplacing = false\n\t\t\tpass\n\t\t\n", "exports": ["@export var active : bool = false"], "functions": ["func _ready() -> void:", "func _process(delta: float) -> void:"], "language": "gd", "path": "res:///Scripts/tools/level.gd", "signals": []}, {"content": "@tool\nextends Sprite2D\n@onready var collision_shape_2d: CollisionShape2D = $\"..\"\n\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tpass # Replace with function body.\n\n\n# Called every frame. 'delta' is the elapsed time since the previous frame.\nfunc _process(_delta: float) -> void:\n\tif Engine.is_editor_hint():\n\t\t\n\t\t#print(collision_shape_2d.shape.get_property_list())\n\t\tscale = Vector2.ONE * collision_shape_2d.shape.get_rect().size.x / (texture.get_size().x/2)\n\t\treturn\n\tpass\n", "exports": [], "functions": ["func _ready() -> void:", "func _process(_delta: float) -> void:"], "language": "gd", "path": "res:///Scripts/tools/planet_atmo.gd", "signals": []}, {"content": "extends Control\n\n# Called when the node enters the scene tree for the first time.\nfunc _ready() -> void:\n\tpass\n\n# Called when Restart button is pressed\nfunc _on_restart_button_pressed() -> void:\n\tGameManager.restart_game()\n\n# Called when Menu button is pressed\nfunc _on_menu_button_pressed() -> void:\n\tGameManager.go_to_main_menu()\n", "exports": [], "functions": ["func _ready() -> void:", "func _on_restart_button_pressed() -> void:", "func _on_menu_button_pressed() -> void:"], "language": "gd", "path": "res:///Scripts/WinScreen.gd", "signals": []}]}