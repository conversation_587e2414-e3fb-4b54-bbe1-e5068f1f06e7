[gd_scene load_steps=4 format=3 uid="uid://din50gkoucxpp"]

[ext_resource type="Script" uid="uid://ew10v6pbwxv6" path="res://Scripts/MainMenu.gd" id="1_main_menu"]
[ext_resource type="Theme" uid="uid://dvwgvp8u8xo0i" path="res://Scenes/UI/Themes/UI_Theme.tres" id="1_mjmdf"]
[ext_resource type="Material" uid="uid://2wo2meekggrg" path="res://Scenes/Effects/StarBackground_Menu.tres" id="3_72sra"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_mjmdf")
script = ExtResource("1_main_menu")

[node name="Background" type="ColorRect" parent="."]
material = ExtResource("3_72sra")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -120.0
offset_right = 100.0
offset_bottom = 120.0
grow_horizontal = 2
grow_vertical = 2

[node name="GameTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "SPACE LOOP"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2

[node name="StartButton" type="Button" parent="VBoxContainer"]
custom_minimum_size = Vector2(200, 50)
layout_mode = 2
text = "START GAME"

[node name="SettingsButton" type="Button" parent="VBoxContainer"]
custom_minimum_size = Vector2(200, 50)
layout_mode = 2
text = "SETTINGS"

[node name="ExitButton" type="Button" parent="VBoxContainer"]
custom_minimum_size = Vector2(200, 50)
layout_mode = 2
text = "EXIT"

[connection signal="pressed" from="VBoxContainer/StartButton" to="." method="_on_start_button_pressed"]
[connection signal="pressed" from="VBoxContainer/SettingsButton" to="." method="_on_settings_button_pressed"]
[connection signal="pressed" from="VBoxContainer/ExitButton" to="." method="_on_exit_button_pressed"]
