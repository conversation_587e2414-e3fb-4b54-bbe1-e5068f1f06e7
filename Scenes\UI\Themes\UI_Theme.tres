[gd_resource type="Theme" load_steps=28 format=3 uid="uid://dvwgvp8u8xo0i"]

[ext_resource type="Texture2D" uid="uid://e46mp4fov066" path="res://Assets/UI/Chosen UIs/check_square_grey_square.png" id="1_f6jyn"]
[ext_resource type="Texture2D" uid="uid://b76cbu5cjfx6g" path="res://Assets/UI/Chosen UIs/button_square.png" id="1_fbw4s"]
[ext_resource type="Texture2D" uid="uid://mchwof6kq82w" path="res://Assets/UI/Chosen UIs/check_square_grey_cross.png" id="1_xky8r"]
[ext_resource type="Texture2D" uid="uid://baroodbijeaia" path="res://Assets/UI/Chosen UIs/check_square_grey_cross-disabled.png" id="2_7memj"]
[ext_resource type="Texture2D" uid="uid://8yaar7tt0hh6" path="res://Assets/UI/Chosen UIs/check_square_grey_square-disabled.png" id="2_j6u7m"]
[ext_resource type="Texture2D" uid="uid://dciikypgn05e7" path="res://Assets/UI/Chosen UIs/check_square_grey-clicked.png" id="3_ldgtl"]
[ext_resource type="Texture2D" uid="uid://delvecdvx0fim" path="res://Assets/UI/Chosen UIs/check_square_grey_square-clicked.png" id="3_wj56m"]
[ext_resource type="Texture2D" uid="uid://bbxenjju8s5wb" path="res://Assets/UI/Chosen UIs/check_square_grey-disabled.png" id="4_04o3o"]
[ext_resource type="Texture2D" uid="uid://iomkb3cpdrcu" path="res://Assets/UI/Chosen UIs/check_square_grey.png" id="5_kcg2t"]
[ext_resource type="Texture2D" uid="uid://d38kw0vkeugql" path="res://Assets/UI/kenney_ui-pack/PNG/Blue/Default/slide_hangle.png" id="5_xky8r"]
[ext_resource type="Texture2D" uid="uid://c4kmxjgwscmma" path="res://Assets/UI/Chosen UIs/SlideHangle_Disabled.png" id="6_7memj"]
[ext_resource type="Texture2D" uid="uid://bhnvgk4rm5sc2" path="res://Assets/UI/Chosen UIs/SlideHangle_Hovered.png" id="7_wj56m"]
[ext_resource type="Texture2D" uid="uid://oay4q1cygy7s" path="res://Assets/UI/Chosen UIs/slide_horizontal_color_section.png" id="8_kcg2t"]
[ext_resource type="Texture2D" uid="uid://c0l6wrgnmnsk4" path="res://Assets/UI/Chosen UIs/slide_horizontal_grey.png" id="9_x151o"]
[ext_resource type="Texture2D" uid="uid://cuqvenqfqveat" path="res://Assets/UI/Chosen UIs/slide_horizontal_color.png" id="13_7memj"]
[ext_resource type="FontFile" uid="uid://bt7d07ityf5rq" path="res://Assets/UI/Fonts/Kenney Future.ttf" id="15_wj56m"]
[ext_resource type="Texture2D" uid="uid://c7b4gtc0v86kl" path="res://Assets/UI/Chosen UIs/button_square_header_blade_rectangle_screws.png" id="16_x151o"]
[ext_resource type="Texture2D" uid="uid://20mtgpjf1vus" path="res://Assets/UI/Chosen UIs/button_square_header_blade_square_screws.png" id="17_xuqoi"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_xuqoi"]
texture = ExtResource("1_fbw4s")
texture_margin_left = 12.0
texture_margin_top = 12.0
texture_margin_right = 12.0
texture_margin_bottom = 12.0
modulate_color = Color(1, 1, 1, 0.501961)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_fbw4s"]
texture = ExtResource("1_fbw4s")
texture_margin_left = 12.0
texture_margin_top = 12.0
texture_margin_right = 12.0
texture_margin_bottom = 12.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_eejyr"]
texture = ExtResource("1_fbw4s")
texture_margin_left = 12.0
texture_margin_top = 12.0
texture_margin_right = 12.0
texture_margin_bottom = 12.0
modulate_color = Color(1, 1, 1, 0.713726)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gpmsd"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_x151o"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(1, 1, 1, 0.75)
draw_center = false
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
corner_radius_top_left = 3
corner_radius_top_right = 3
corner_radius_bottom_right = 3
corner_radius_bottom_left = 3
corner_detail = 5
expand_margin_left = 2.0
expand_margin_top = 2.0
expand_margin_right = 2.0
expand_margin_bottom = 2.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_kcg2t"]
texture = ExtResource("13_7memj")
texture_margin_left = 16.0
texture_margin_right = 16.0
expand_margin_top = 8.0
expand_margin_bottom = 8.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_sxqgh"]
texture = ExtResource("9_x151o")
texture_margin_left = 16.0
texture_margin_right = 16.0
expand_margin_top = 8.0
expand_margin_bottom = 8.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_gpmsd"]
texture = ExtResource("16_x151o")
texture_margin_left = 16.0
texture_margin_top = 34.0
texture_margin_right = 16.0
texture_margin_bottom = 16.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_j07o8"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 8.0
texture = ExtResource("17_xuqoi")
texture_margin_left = 16.0
texture_margin_top = 48.0
texture_margin_right = 16.0
texture_margin_bottom = 16.0

[resource]
default_font = ExtResource("15_wj56m")
default_font_size = 15
Button/colors/font_color = Color(0, 0, 0, 1)
Button/colors/font_disabled_color = Color(0, 0, 0, 0.501961)
Button/colors/font_focus_color = Color(0, 0, 0, 1)
Button/colors/font_hover_color = Color(0, 0, 0, 1)
Button/colors/font_hover_pressed_color = Color(0, 0, 0, 1)
Button/colors/font_outline_color = Color(1, 1, 1, 1)
Button/colors/font_pressed_color = Color(0, 0, 0, 1)
Button/colors/icon_disabled_color = Color(0, 0, 0, 0.4)
Button/colors/icon_focus_color = Color(0, 0, 0, 1)
Button/colors/icon_hover_color = Color(0, 0, 0, 1)
Button/colors/icon_hover_pressed_color = Color(0, 0, 0, 1)
Button/colors/icon_normal_color = Color(0, 0, 0, 1)
Button/colors/icon_pressed_color = Color(0, 0, 0, 1)
Button/constants/align_to_largest_stylebox = 0
Button/constants/h_separation = 4
Button/constants/icon_max_width = 0
Button/constants/line_spacing = 0
Button/constants/outline_size = 0
Button/font_sizes/font_size = 13
Button/styles/hover = SubResource("StyleBoxTexture_xuqoi")
Button/styles/normal = SubResource("StyleBoxTexture_fbw4s")
Button/styles/pressed = SubResource("StyleBoxTexture_eejyr")
CheckBox/colors/font_color = Color(0.875, 0.875, 0.875, 1)
CheckBox/colors/font_disabled_color = Color(0.875, 0.875, 0.875, 0.5)
CheckBox/colors/font_focus_color = Color(0.95, 0.95, 0.95, 1)
CheckBox/colors/font_hover_color = Color(0.95, 0.95, 0.95, 1)
CheckBox/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
CheckBox/colors/font_outline_color = Color(0, 0, 0, 1)
CheckBox/colors/font_pressed_color = Color(1, 1, 1, 1)
CheckBox/constants/check_v_offset = 0
CheckBox/constants/h_separation = 4
CheckBox/constants/outline_size = 0
CheckBox/icons/checked = ExtResource("1_xky8r")
CheckBox/icons/checked_disabled = ExtResource("2_7memj")
CheckBox/icons/radio_checked = ExtResource("3_wj56m")
CheckBox/icons/radio_checked_disabled = ExtResource("2_j6u7m")
CheckBox/icons/radio_unchecked = ExtResource("5_kcg2t")
CheckBox/icons/radio_unchecked_disabled = ExtResource("4_04o3o")
CheckBox/icons/unchecked = ExtResource("5_kcg2t")
CheckBox/icons/unchecked_disabled = ExtResource("4_04o3o")
CheckBox/styles/disabled = SubResource("StyleBoxEmpty_gpmsd")
CheckBox/styles/focus = SubResource("StyleBoxFlat_x151o")
CheckBox/styles/hover = SubResource("StyleBoxEmpty_gpmsd")
CheckBox/styles/hover_pressed = SubResource("StyleBoxEmpty_gpmsd")
CheckBox/styles/normal = SubResource("StyleBoxEmpty_gpmsd")
CheckBox/styles/pressed = SubResource("StyleBoxEmpty_gpmsd")
CheckButton/icons/checked = ExtResource("1_f6jyn")
CheckButton/icons/checked_disabled = ExtResource("2_j6u7m")
CheckButton/icons/checked_disabled_mirrored = null
CheckButton/icons/checked_mirrored = null
CheckButton/icons/unchecked = ExtResource("3_ldgtl")
CheckButton/icons/unchecked_disabled = ExtResource("4_04o3o")
HSlider/constants/center_grabber = 0
HSlider/constants/grabber_offset = 0
HSlider/icons/grabber = ExtResource("5_xky8r")
HSlider/icons/grabber_disabled = ExtResource("6_7memj")
HSlider/icons/grabber_highlight = ExtResource("7_wj56m")
HSlider/icons/tick = ExtResource("8_kcg2t")
HSlider/styles/grabber_area = SubResource("StyleBoxTexture_kcg2t")
HSlider/styles/grabber_area_highlight = SubResource("StyleBoxTexture_kcg2t")
HSlider/styles/slider = SubResource("StyleBoxTexture_sxqgh")
Panel/styles/panel = SubResource("StyleBoxTexture_gpmsd")
PanelContainer/styles/panel = SubResource("StyleBoxTexture_j07o8")
